"use client";

import axios from 'axios';
import { useAuth } from './auth-context';
import { useCallback } from 'react';
import { SubscriptionTier } from './usage-service';
import { logger } from '@/lib/logger';

// Mapping our application tiers to Paystack plan codes
// These will need to be replaced with actual plan codes from your Paystack dashboard
const PLAN_CODES: Record<string, string> = {
  'BASIC': 'PLN_exwlsxe2x9db6h3',
  'PRO': 'PLN_1uq5sio6o2yag1b',
};

// Interface for the initialize payment response
interface InitializePaymentResponse {
  authorization_url: string;
  access_code: string;
  reference: string;
}

// Interface for subscription data
export interface PaystackSubscription {
  id: number;
  subscription_code: string;
  email_token: string;
  amount: number;
  status: 'active' | 'cancelled' | 'completed' | 'non-renewing' | 'attention';
  plan: {
    name: string;
    plan_code: string;
  };
  next_payment_date: string;
}

/**
 * Initialize a subscription transaction
 * This is used to initiate the payment process for a subscription
 */
export async function initializeSubscription(
  email: string,
  tier: SubscriptionTier
): Promise<InitializePaymentResponse> {
  if (tier === 'FREE') {
    throw new Error('Cannot create subscription for FREE tier');
  }

  const planCode = PLAN_CODES[tier];
  if (!planCode) {
    throw new Error(`Invalid subscription tier: ${tier}`);
  }

  try {
    // We're using the Next.js API route to proxy the request to Paystack
    // This keeps the secret key on the server
    const response = await axios.post('/api/paystack/initialize-transaction', {
      email,
      plan: planCode,
    });

    if (response.data.status) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to initialize payment');
    }
  } catch (error) {
    logger.error('Error initializing subscription:', error);
    throw error;
  }
}

/**
 * Verify a transaction by reference
 * This is used to confirm if a payment was successful
 */
export async function verifyTransaction(reference: string): Promise<boolean> {
  try {
    const response = await axios.get(`/api/paystack/verify-transaction/${reference}`);
    return response.data.status && response.data.data.status === 'success';
  } catch (error) {
    logger.error('Error verifying transaction:', error);
    return false;
  }
}

/**
 * Get active subscription for a user
 */
export async function getUserSubscription(email: string): Promise<PaystackSubscription | null> {
  try {
    logger.log(`[PaystackService] Getting subscription for email: ${email}`);
    const response = await axios.get(`/api/paystack/subscription/${email}`);
    
    logger.log(`[PaystackService] Subscription API response:`, {
      status: response.data.status,
      hasData: !!response.data.data,
      dataLength: response.data.data ? response.data.data.length : 0
    });
    
    if (response.data.status && response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
      // Look for any subscription with status containing 'active'
      // This handles 'active', 'active (renewing)', etc.
      const allSubscriptions = response.data.data;
      logger.log(`[PaystackService] Found ${allSubscriptions.length} subscriptions:`, 
        allSubscriptions.map((sub: PaystackSubscription) => ({
          code: sub.subscription_code,
          status: sub.status,
          plan: sub.plan?.name || 'Unknown'
        }))
      );
      
      // First look for simple 'active' status
      let activeSubscription = allSubscriptions.find(
        (sub: PaystackSubscription) => sub.status === 'active'
      );
      
      // If no exact 'active' status found, look for any status containing 'active'
      if (!activeSubscription) {
        activeSubscription = allSubscriptions.find(
          (sub: PaystackSubscription) => 
            typeof sub.status === 'string' && 
            sub.status.toLowerCase().includes('active')
        );
      }
      
      if (activeSubscription) {
        logger.log(`[PaystackService] Found active subscription:`, {
          code: activeSubscription.subscription_code,
          status: activeSubscription.status,
          plan: activeSubscription.plan?.name || 'Unknown'
        });
      } else {
        logger.log(`[PaystackService] No active subscription found among existing subscriptions`);
      }
      
      return activeSubscription || null;
    }
    logger.log(`[PaystackService] No subscriptions found for email: ${email}`);
    return null;
  } catch (error) {
    logger.error('[PaystackService] Error getting user subscription:', error);
    // Don't throw the error, just return null to indicate no active subscription
    return null;
  }
}

/**
 * Cancel a subscription
 */
export async function cancelSubscription(subscriptionCode: string, emailToken: string): Promise<boolean> {
  try {
    logger.log(`Attempting to cancel subscription: ${subscriptionCode}`);
    const response = await axios.post('/api/paystack/cancel-subscription', {
      code: subscriptionCode,
      token: emailToken
    });
    
    if (response.data.status) {
      logger.log(`Successfully cancelled subscription: ${subscriptionCode}`);
      return true;
    } else {
      logger.error('Subscription cancellation failed:', response.data.message);
      return false;
    }
  } catch (error: any) {
    logger.error('Error canceling subscription:', error.response?.data || error.message);
    return false;
  }
}

/**
 * React hook for using Paystack services
 */
export function usePaystackService() {
  const { user } = useAuth();

  const initializeUserSubscription = useCallback(
    async (tier: SubscriptionTier) => {
      if (!user || !user.email) {
        throw new Error('User must be logged in to create subscription');
      }
      return initializeSubscription(user.email, tier);
    },
    [user]
  );

  const getUserActiveSubscription = useCallback(async () => {
    if (!user || !user.email) {
      return null;
    }
    return getUserSubscription(user.email);
  }, [user]);

  const cancelUserSubscription = useCallback(
    async (subscriptionCode: string, emailToken: string) => {
      if (!user) {
        throw new Error('User must be logged in to cancel subscription');
      }
      return cancelSubscription(subscriptionCode, emailToken);
    },
    [user]
  );

  return {
    initializeSubscription: initializeUserSubscription,
    getSubscription: getUserActiveSubscription,
    cancelSubscription: cancelUserSubscription,
    verifyTransaction,
  };
} 