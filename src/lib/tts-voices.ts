import { logger } from '@/lib/logger';

// Available voice options from ElevenLabs premade voice library
// Selected for diverse characteristics and high quality
export const AVAILABLE_TTS_VOICES = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>'
] as const;

export type TTSVoiceName = typeof AVAILABLE_TTS_VOICES[number];

// Default voice settings
export const DEFAULT_TTS_VOICES = {
  questionVoice: '<PERSON>' as TTSVoiceName,
  answerVoice: '<PERSON>' as TTSVoiceName,
} as const;

// Voice descriptions for better user experience
// Based on actual ElevenLabs premade voice characteristics
export const VOICE_DESCRIPTIONS: Record<TTSVoiceName, string> = {
  'Adam': 'Deep male voice, American accent - ideal for narration',
  '<PERSON>': 'Calm young female voice, American accent - soothing',
  '<PERSON>': 'Raspy middle-aged male, British accent - distinctive',
  '<PERSON>': 'Soft young female voice, American accent - gentle',
  'Domi': 'Strong young female voice, American accent - impactful',
  '<PERSON>': 'Deep young male voice, American accent - powerful',
  '<PERSON>': 'Seductive middle-aged female, English-Swedish accent',
  '<PERSON>': 'Casual middle-aged male, Australian accent - conversational',
  'Fin': 'Old male voice, Irish accent - sailor character',
  'Dave': 'Young male voice, British-Essex accent - conversational',
  'Dorothy': 'Pleasant young female, British accent - children\'s stories',
  'Matilda': 'Warm young female voice, American accent - audiobooks',
  'Daniel': 'Deep middle-aged male, British accent - news presenter',
  'Emily': 'Calm young female, American accent - meditation',
  'Antoni': 'Well-rounded young male, American accent - versatile'
};

/**
 * Validates if a voice name is available in our ElevenLabs voice selection
 */
export function isValidTTSVoice(voice: string): voice is TTSVoiceName {
  return AVAILABLE_TTS_VOICES.includes(voice as TTSVoiceName);
}

/**
 * Gets a safe voice name, falling back to default if invalid
 */
export function getSafeVoiceName(voice: string | undefined, defaultVoice: TTSVoiceName): TTSVoiceName {
  if (!voice || !isValidTTSVoice(voice)) {
    return defaultVoice;
  }
  return voice;
}

/**
 * Fetches pre-recorded audio file from Vercel Blob storage for voice previews
 * @param voiceName The voice name to fetch audio for
 * @param type Either 'question' or 'answer' for the sample type
 * @returns Promise<string> URL of the audio file
 */
export async function fetchPrerecordedAudio(voiceName: TTSVoiceName, type: 'question' | 'answer'): Promise<string> {
  try {
    // Construct the blob path based on the naming convention
    const blobPath = `tts-settings/${voiceName}-${type}`;

    // Fetch the list of blobs to find the exact file
    const response = await fetch('/api/blob-list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prefix: blobPath }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch blob list: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Failed to list blobs');
    }

    // Find the matching blob
    const matchingBlob = data.blobs?.find((blob: any) =>
      blob.pathname.startsWith(blobPath) && blob.pathname.endsWith('.wav')
    );

    if (!matchingBlob) {
      throw new Error(`Pre-recorded audio not found for voice: ${voiceName} (${type}). Expected path: ${blobPath}.wav`);
    }

    logger.log(`[TTS Voices] Found pre-recorded audio for ${voiceName} (${type}):`, matchingBlob.url);
    return matchingBlob.url;
  } catch (error) {
    logger.error(`Error fetching pre-recorded audio for ${voiceName} (${type}):`, error);
    throw error;
  }
}
