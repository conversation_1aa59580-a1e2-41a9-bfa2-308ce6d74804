import { ElevenLabsClient } from '@elevenlabs/elevenlabs-js';
import { logger } from './logger';

// ElevenLabs client instance
let elevenLabsClient: ElevenLabsClient | null = null;

function getElevenLabsClient(customApiKey?: string): ElevenLabsClient {
  // If a custom API key is provided, create a new client instance
  if (customApiKey) {
    return new ElevenLabsClient({ apiKey: customApiKey });
  }

  // Otherwise, use the cached client with the application's API key
  if (!elevenLabsClient) {
    const apiKey = process.env.ELEVENLABS_API_KEY;
    if (!apiKey) {
      throw new Error('ELEVENLABS_API_KEY environment variable is required');
    }
    elevenLabsClient = new ElevenLabsClient({ apiKey });
  }
  return elevenLabsClient;
}

// Voice mapping using ElevenLabs premade voices
// Selected from the public ElevenLabs voice library for diverse characteristics
export const VOICE_MAPPING: Record<string, string> = {
  // Professional & Clear Voices
  'Adam': 'pNInz6obpgDQGcFmaJgB', // Deep male voice, American accent, ideal for narration
  'Rachel': '21m00Tcm4TlvDq8ikWAM', // Calm young female voice, American accent, soothing
  '<PERSON>': 'JBFqnCBsd6RMkjVDRZzb', // Raspy middle-aged male, British accent, distinctive
  'Sarah': 'EXAVITQu4vr4xnSDxMaL', // Soft young female voice, American accent, gentle

  // Character & Personality Voices
  'Domi': 'AZnzlk1XvdvUeBnXmlld', // Strong young female voice, American accent, impactful
  'Josh': 'TxGEqnHWrfWFTfGW9XjX', // Deep young male voice, American accent, powerful
  'Charlotte': 'XB0fDUnXU5powFXDhCwa', // Seductive middle-aged female, English-Swedish accent
  'Charlie': 'IKne3meq5aSn9XLyUdCD', // Casual middle-aged male, Australian accent, conversational

  // Distinctive & Character Voices
  'Fin': 'D38z5RcWu1voky8WS1ja', // Old male voice, Irish accent, sailor character
  'Dave': 'CYw3kZ02Hs0563khs1Fj', // Young male voice, British-Essex accent, conversational
  'Dorothy': 'ThT5KcBeYPX3keUQqHPh', // Pleasant young female, British accent, children's stories
  'Matilda': 'XrExE9yKIg1WjnnlVkGX', // Warm young female voice, American accent, audiobooks

  // Additional Variety
  'Daniel': 'onwK4e9ZLuTAKqWW03F9', // Deep middle-aged male, British accent, news presenter
  'Emily': 'LcfcDJNUP1GQjkzn1xUU', // Calm young female, American accent, meditation
  'Antoni': 'ErXwobaYiN019PkySvjV', // Well-rounded young male, American accent, versatile
};

// Reverse mapping for validation
export const ELEVENLABS_TO_VOICE_NAME: Record<string, string> = Object.fromEntries(
  Object.entries(VOICE_MAPPING).map(([voiceName, elevenLabsId]) => [elevenLabsId, voiceName])
);

/**
 * Convert voice name to ElevenLabs voice ID
 */
export function getElevenLabsVoiceId(voiceName: string): string {
  const voiceId = VOICE_MAPPING[voiceName];
  if (!voiceId) {
    logger.warn(`Unknown voice: ${voiceName}, falling back to default (Rachel)`);
    return VOICE_MAPPING['Rachel']; // Default to Rachel (calm, professional voice)
  }
  return voiceId;
}

/**
 * Generate speech using ElevenLabs TTS API
 * Uses Flash v2.5 model for ultra-fast generation (75ms latency) and 50% cost reduction
 * Returns audio data as base64 encoded MP3
 */
export async function generateSpeechElevenLabs(
  text: string,
  voiceName: string = 'Rachel',
  customApiKey?: string
): Promise<string> {
  try {
    logger.log('[ElevenLabs TTS] Generating speech:', {
      textLength: text.length,
      voiceName,
      textPreview: text.substring(0, 100) + (text.length > 100 ? '...' : '')
    });

    const client = getElevenLabsClient(customApiKey);
    const voiceId = getElevenLabsVoiceId(voiceName);

    logger.log('[ElevenLabs TTS] Using voice ID:', voiceId, 'for voice:', voiceName);

    // Generate speech using ElevenLabs
    const audioStream = await client.textToSpeech.convert(voiceId, {
      text: text,
      modelId: 'eleven_flash_v2_5', // Flash v2.5 - Ultra-low latency, 75ms model latency, 50% lower cost
      voiceSettings: {
        stability: 0.5,
        similarityBoost: 0.75,
        style: 0.0,
        useSpeakerBoost: true
      }
    });

    // Convert stream to buffer
    const chunks: Uint8Array[] = [];

    // Handle the readable stream
    for await (const chunk of audioStream) {
      if (chunk instanceof Uint8Array) {
        chunks.push(chunk);
      }
    }

    // Combine all chunks into a single buffer
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const audioBuffer = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of chunks) {
      audioBuffer.set(chunk, offset);
      offset += chunk.length;
    }

    // Convert to base64 (same format as Gemini output)
    const base64Audio = Buffer.from(audioBuffer).toString('base64');

    logger.log('[ElevenLabs TTS] Successfully generated audio:', {
      audioSizeBytes: audioBuffer.length,
      base64Length: base64Audio.length
    });

    return base64Audio;

  } catch (error) {
    logger.error('[ElevenLabs TTS] Error generating speech:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('quota') || error.message.includes('limit')) {
        throw new Error('ElevenLabs API quota exceeded. Please try again later.');
      } else if (error.message.includes('unauthorized') || error.message.includes('401')) {
        throw new Error('ElevenLabs API key is invalid or missing.');
      } else if (error.message.includes('timeout')) {
        throw new Error('ElevenLabs API request timed out. Please try again.');
      } else {
        throw new Error(`ElevenLabs TTS generation failed: ${error.message}`);
      }
    }
    
    throw new Error('Unknown error occurred during speech generation');
  }
}

/**
 * Validate if a voice name is supported
 */
export function isValidElevenLabsVoice(voiceName: string): boolean {
  return voiceName in VOICE_MAPPING;
}

/**
 * Get all available voice names from ElevenLabs mapping
 */
export function getAvailableVoiceNames(): string[] {
  return Object.keys(VOICE_MAPPING);
}
