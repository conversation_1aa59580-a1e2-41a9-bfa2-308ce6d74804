"use client";

import React from 'react';
import { logger } from '@/lib/logger';

// Exchange rate cache interface
interface ExchangeRateCache {
  rate: number;
  timestamp: number;
  source: 'api' | 'fallback';
}

// Cache duration: 1 hour (3600000 ms)
const CACHE_DURATION = 60 * 60 * 1000;

// Fallback rate: 1 ZAR = 0.0558 USD (updated June 2025)
const FALLBACK_RATE = 0.0558;

// In-memory cache for exchange rates
let exchangeRateCache: ExchangeRateCache | null = null;

/**
 * Fetches the current USD to ZAR exchange rate from exchangerate-api.com v6
 * Falls back to a static rate if the API is unavailable
 */
async function fetchExchangeRate(): Promise<number> {
  try {
    // Use exchangerate-api.com v6 with API key
    const response = await fetch('https://v6.exchangerate-api.com/v6/************************/latest/USD', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });

    if (!response.ok) {
      throw new Error(`Exchange rate API responded with status: ${response.status}`);
    }

    const data = await response.json();

    if (!data.conversion_rates || typeof data.conversion_rates.ZAR !== 'number') {
      throw new Error('Invalid response format from exchange rate API');
    }

    // Convert USD to ZAR rate to ZAR to USD rate
    const usdToZarRate = data.conversion_rates.ZAR;
    const zarToUsdRate = 1 / usdToZarRate;

    logger.log('[Currency Service] Successfully fetched exchange rate:', {
      usdToZarRate,
      zarToUsdRate,
      source: 'api'
    });

    return zarToUsdRate;
  } catch (error) {
    logger.error('[Currency Service] Failed to fetch exchange rate from API:', error);
    throw error;
  }
}

/**
 * Gets the ZAR to USD exchange rate with caching
 * Returns cached rate if available and not expired, otherwise fetches new rate
 */
export async function getZarToUsdRate(): Promise<ExchangeRateCache> {
  const now = Date.now();

  // Check if we have a valid cached rate
  if (exchangeRateCache && (now - exchangeRateCache.timestamp) < CACHE_DURATION) {
    logger.log('[Currency Service] Using cached exchange rate:', exchangeRateCache);
    return exchangeRateCache;
  }

  try {
    // Try to fetch fresh rate from API
    const rate = await fetchExchangeRate();
    
    exchangeRateCache = {
      rate,
      timestamp: now,
      source: 'api'
    };

    return exchangeRateCache;
  } catch (error) {
    logger.warn('[Currency Service] API failed, using fallback rate:', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      fallbackRate: FALLBACK_RATE 
    });

    // Use fallback rate
    exchangeRateCache = {
      rate: FALLBACK_RATE,
      timestamp: now,
      source: 'fallback'
    };

    return exchangeRateCache;
  }
}

/**
 * Converts ZAR amount to USD using current exchange rate
 */
export async function convertZarToUsd(zarAmount: number): Promise<{
  usdAmount: number;
  rate: number;
  source: 'api' | 'fallback';
  formatted: string;
}> {
  const { rate, source } = await getZarToUsdRate();
  const usdAmount = zarAmount * rate;
  
  // Format to 2 decimal places
  const formatted = `$${usdAmount.toFixed(2)}`;

  return {
    usdAmount,
    rate,
    source,
    formatted
  };
}

/**
 * Formats a ZAR price with USD equivalent
 * Example: "R29 (~$1.50)"
 */
export async function formatPriceWithUsd(zarAmount: number): Promise<string> {
  if (zarAmount === 0) {
    return 'R0 (~$0)';
  }

  try {
    const { formatted } = await convertZarToUsd(zarAmount);
    return `R${zarAmount} (~${formatted})`;
  } catch (error) {
    logger.error('[Currency Service] Failed to format price with USD:', error);
    // Fallback to ZAR only if conversion fails completely
    return `R${zarAmount}`;
  }
}

/**
 * React hook for currency conversion with loading state
 */
export function useCurrencyConversion() {
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const convertPrice = async (zarAmount: number): Promise<string> => {
    if (zarAmount === 0) {
      return 'R0 (~$0)';
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await formatPriceWithUsd(zarAmount);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Currency conversion failed';
      setError(errorMessage);
      logger.error('[Currency Hook] Conversion failed:', err);
      return `R${zarAmount}`; // Fallback to ZAR only
    } finally {
      setIsLoading(false);
    }
  };

  return {
    convertPrice,
    isLoading,
    error
  };
}


