"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth-context';
import { useUsageService, SubscriptionTier, USAGE_LIMITS } from '@/lib/usage-service';
import { usePaystackService, PaystackSubscription } from '@/lib/paystack-service';
import { useSubscription } from '@/hooks/use-subscription';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Badge } from './badge';
import { Loader2, CheckCircle, AlertCircle, CreditCard } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { logger } from '@/lib/logger';

export function TierManagement() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  const { updateTier, LIMITS } = useUsageService();
  const { initializeSubscription, getSubscription, cancelSubscription } = usePaystackService();
  const { tier: currentTier, isLoading: subscriptionLoading, refreshSubscription } = useSubscription();

  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isCanceling, setIsCanceling] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [activeSubscription, setActiveSubscription] = useState<PaystackSubscription | null>(null);

  useEffect(() => {
    const loadSubscriptionDetails = async () => {
      if (user && currentTier !== 'FREE') {
        setIsLoading(true);
        try {
          logger.log(`[TierManagement] User has paid tier (${currentTier}), loading subscription details...`);
          const subscription = await getSubscription();
          logger.log('[TierManagement] Subscription loaded:', subscription);

          if (subscription) {
            logger.log(`[TierManagement] Active subscription found:`, {
              code: subscription.subscription_code,
              status: subscription.status,
              plan: subscription.plan?.name || 'Unknown',
              next_payment: subscription.next_payment_date
            });
          } else {
            logger.log(`[TierManagement] No active subscription found for user`);
          }

          setActiveSubscription(subscription);
        } catch (subscriptionError) {
          logger.error('[TierManagement] Error loading subscription:', subscriptionError);
          setActiveSubscription(null);
        } finally {
          setIsLoading(false);
        }
      } else {
        logger.log('[TierManagement] User has FREE tier, no subscription to load');
        setActiveSubscription(null);
      }
    };

    // Only load subscription details if we have a user and tier info is loaded
    if (user && !subscriptionLoading) {
      loadSubscriptionDetails();
    }
  }, [user, currentTier, subscriptionLoading, getSubscription]);

  const handleUpgrade = async (newTier: SubscriptionTier) => {
    if (newTier === 'FREE') {
      logger.log(`[TierManagement] Handling downgrade to FREE, activeSubscription:`, activeSubscription);
      // For free tier, we need to show confirmation dialog if there's an active subscription
      if (activeSubscription) {
        logger.log(`[TierManagement] Showing confirmation dialog for subscription cancellation`);
        setShowCancelConfirm(true);
      } else {
        logger.log(`[TierManagement] No active subscription found, updating tier directly`);
        // No active subscription, just update tier
        setIsLoading(true);
        try {
          await updateTier('FREE');
          await refreshSubscription(); // Refresh subscription context

          toast({
            title: "Plan Updated",
            description: "Your plan has been downgraded to FREE.",
          });
        } catch (error) {
          logger.error('Error downgrading plan:', error);
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to downgrade your plan",
          });
        } finally {
          setIsLoading(false);
        }
      }
      return;
    }

    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please sign in to upgrade your plan.",
      });
      router.push('/auth/signin');
      return;
    }

    setIsInitializing(true);
    logger.log(`[TierManagement] Initializing upgrade to ${newTier} tier`);

    try {
      // Check if user already has an active subscription
      if (activeSubscription) {
        logger.log("[TierManagement] User has existing subscription. Attempting to cancel before upgrade...", activeSubscription);
        // Cancel existing subscription before creating a new one
        const result = await cancelSubscription(
          activeSubscription.subscription_code,
          activeSubscription.email_token
        );

        if (!result) {
          logger.error("[TierManagement] Failed to cancel existing subscription. Continuing with upgrade anyway...");
          toast({
            variant: "destructive",
            title: "Warning",
            description: "Could not cancel existing subscription. You may need to cancel manually.",
          });
          // We'll continue with the upgrade despite cancellation failure
        } else {
          logger.log("[TierManagement] Existing subscription cancelled successfully");

          // Add a longer delay to ensure Paystack's system registers the cancellation
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

        // Update local state regardless of API success
        setActiveSubscription(null);
      } else {
        logger.log("[TierManagement] No existing subscription found, proceeding with new subscription");
      }

      // Store the intended tier in localStorage to verify later
      localStorage.setItem('pendingSubscriptionTier', newTier);
      logger.log(`[TierManagement] Saved pending subscription tier to localStorage: ${newTier}`);

      // Initialize payment with Paystack
      logger.log(`[TierManagement] Initializing payment with Paystack for tier: ${newTier}`);
      const response = await initializeSubscription(newTier);

      // Redirect to Paystack checkout page
      if (response && response.authorization_url) {
        logger.log(`[TierManagement] Redirecting to Paystack checkout: ${response.authorization_url}`);
        // Redirect to Paystack checkout page
        window.location.href = response.authorization_url;
      } else {
        throw new Error('Failed to initialize payment');
      }
    } catch (error) {
      logger.error('[TierManagement] Error initializing payment:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to initialize payment process",
      });
      // Clear the localStorage item if payment fails
      localStorage.removeItem('pendingSubscriptionTier');
    } finally {
      setIsInitializing(false);
    }
  };

  const handleCancelSubscription = async () => {
    // Start the loading state
    setIsCanceling(true);
    logger.log(`[TierManagement] Starting subscription cancellation, activeSubscription:`, activeSubscription);

    try {
      // First, ensure we update the tier in the usage service regardless of subscription
      logger.log("[TierManagement] Updating user tier to FREE in the usage service");
      await updateTier('FREE');

      // If there's an active subscription, cancel it with Paystack
      if (activeSubscription) {
        logger.log(`[TierManagement] Attempting to cancel Paystack subscription:`, {
          code: activeSubscription.subscription_code,
          status: activeSubscription.status,
          plan: activeSubscription.plan?.name || 'Unknown'
        });

        const result = await cancelSubscription(
          activeSubscription.subscription_code,
          activeSubscription.email_token
        );

        // Check if we got a proper success response from Paystack
        logger.log(`[TierManagement] Paystack cancellation result:`, result);

        if (result === true) {
          logger.log("[TierManagement] Subscription cancellation confirmed by Paystack");

          // Update local state and refresh subscription context
          setActiveSubscription(null);
          await refreshSubscription();

          toast({
            title: "Subscription Cancelled",
            description: "Your subscription has been cancelled and you've been downgraded to the FREE plan.",
          });
        } else {
          logger.error("[TierManagement] Paystack did not confirm subscription cancellation:", result);

          // Update local tier but warn user about Paystack
          await refreshSubscription();

          toast({
            variant: "destructive",
            title: "Partial Success",
            description: "Your account has been downgraded to FREE, but there was an issue cancelling your Paystack subscription. You may need to cancel it manually.",
          });
        }
      } else {
        logger.log("[TierManagement] No active subscription to cancel in Paystack, only updating local tier");
        // No active subscription, just update the UI
        await refreshSubscription();

        toast({
          title: "Plan Updated",
          description: "Your plan has been downgraded to FREE.",
        });
      }
    } catch (error) {
      logger.error('[TierManagement] Error during downgrade/cancellation process:', error);

      toast({
        variant: "destructive",
        title: "Error",
        description: "There was a problem updating your subscription. Please try again or contact support.",
      });
    } finally {
      setIsCanceling(false);
      setShowCancelConfirm(false);
    }
  };

  const handleDowngradeToFree = async () => {
    // This function is now redundant as we're using handleCancelSubscription directly
    // Keeping it for backwards compatibility
    if (activeSubscription) {
      setShowCancelConfirm(true);
    } else {
      setIsLoading(true);
      try {
        await updateTier('FREE');
        await refreshSubscription(); // Refresh subscription context

        toast({
          title: "Plan Updated",
          description: "Your plan has been downgraded to FREE.",
        });
      } catch (error) {
        logger.error('Error downgrading plan:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to downgrade your plan",
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  if (loading || isLoading || subscriptionLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Plans</CardTitle>
          <CardDescription>Sign in to manage your subscription</CardDescription>
        </CardHeader>
        <CardFooter>
          <Button onClick={() => router.push('/auth/signin')}>Sign In</Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <>
      {activeSubscription && (
        <div className="mb-8 p-4 bg-primary/10 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium">Active Subscription</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Next payment: {new Date(activeSubscription.next_payment_date).toLocaleDateString()}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Plan: {activeSubscription.plan?.name || "Unknown"} (Status: {activeSubscription.status})
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="text-destructive hover:text-destructive hover:bg-destructive/10"
              onClick={() => setShowCancelConfirm(true)}
              disabled={isCanceling}
            >
              {isCanceling ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Cancel Subscription
            </Button>
          </div>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-3">
        {/* Free Tier */}
        <Card className={currentTier === 'FREE' ? 'border-primary' : ''}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle>Free</CardTitle>
              {currentTier === 'FREE' && (
                <Badge variant="outline" className="bg-primary/10 text-primary">
                  Current Plan
                </Badge>
              )}
            </div>
            <CardDescription>Basic flashcard generation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <p className="text-3xl font-bold">R0</p>
              <p className="text-sm text-muted-foreground">Free forever</p>
            </div>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.FREE.DAILY_UPLOADS} uploads per day</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.FREE.MONTHLY_UPLOADS} uploads per month</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Basic flashcard generation</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              className="w-full"
              disabled={currentTier === 'FREE' || isInitializing || isCanceling}
              onClick={() => handleUpgrade('FREE')}
            >
              {isInitializing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              {currentTier === 'FREE' ? 'Current Plan' : 'Downgrade'}
            </Button>
          </CardFooter>
        </Card>

        {/* Basic Tier */}
        <Card className={currentTier === 'BASIC' ? 'border-primary' : ''}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle>Basic</CardTitle>
              {currentTier === 'BASIC' && (
                <Badge variant="outline" className="bg-primary/10 text-primary">
                  Current Plan
                </Badge>
              )}
            </div>
            <CardDescription>For regular users</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <p className="text-3xl font-bold">R29</p>
              <p className="text-sm text-muted-foreground">per month</p>
            </div>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.BASIC.DAILY_UPLOADS} uploads per day</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.BASIC.MONTHLY_UPLOADS} uploads per month</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Enhanced flashcard generation</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              variant={currentTier === 'BASIC' ? 'outline' : 'default'}
              className="w-full"
              disabled={currentTier === 'BASIC' || isInitializing || isCanceling}
              onClick={() => handleUpgrade('BASIC')}
            >
              {isInitializing && currentTier !== 'BASIC' ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : currentTier !== 'BASIC' ? (
                <CreditCard className="mr-2 h-4 w-4" />
              ) : null}
              {currentTier === 'BASIC' ? 'Current Plan' : currentTier === 'PRO' ? 'Downgrade' : 'Upgrade'}
            </Button>
          </CardFooter>
        </Card>

        {/* Pro Tier */}
        <Card className={currentTier === 'PRO' ? 'border-primary' : ''}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle>Pro</CardTitle>
              {currentTier === 'PRO' && (
                <Badge variant="outline" className="bg-primary/10 text-primary">
                  Current Plan
                </Badge>
              )}
            </div>
            <CardDescription>For power users</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <p className="text-3xl font-bold">R75</p>
              <p className="text-sm text-muted-foreground">per month</p>
            </div>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.PRO.DAILY_UPLOADS} uploads per day</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>{LIMITS.PRO.MONTHLY_UPLOADS} uploads per month</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Premium flashcard generation</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Text-to-speech for flashcards</span>
              </li>
              <li className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary" />
                <span>Priority processing</span>
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button
              variant={currentTier === 'PRO' ? 'outline' : 'default'}
              className="w-full"
              disabled={currentTier === 'PRO' || isInitializing || isCanceling}
              onClick={() => handleUpgrade('PRO')}
            >
              {isInitializing && currentTier !== 'PRO' ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : currentTier !== 'PRO' ? (
                <CreditCard className="mr-2 h-4 w-4" />
              ) : null}
              {currentTier === 'PRO' ? 'Current Plan' : 'Upgrade'}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Cancel Confirmation Dialog */}
      <AlertDialog open={showCancelConfirm} onOpenChange={setShowCancelConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              {currentTier === 'FREE' ? 'Cancel Subscription' : 'Downgrade to Free Plan'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {currentTier === 'FREE'
                ? "Are you sure you want to cancel your subscription? You will lose access to premium features immediately."
                : `Are you sure you want to downgrade from ${currentTier} to FREE? Your current subscription will be cancelled and you will lose access to premium features immediately.`
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowCancelConfirm(false)} disabled={isCanceling}>
              {currentTier === 'FREE' ? 'Keep Subscription' : 'Stay on Current Plan'}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelSubscription}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isCanceling}
            >
              {isCanceling ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              {currentTier === 'FREE' ? 'Cancel Subscription' : 'Downgrade to Free'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}