"use client";

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Volume2, VolumeX, Loader2 } from 'lucide-react';
import { usePrerecordedAudio } from '@/hooks/use-prerecorded-audio';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { type TTSVoiceName } from '@/lib/tts-voices';

interface PrerecordedSpeakerButtonProps {
  voiceName: TTSVoiceName;
  type: 'question' | 'answer';
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  onClick?: (e: React.MouseEvent) => void;
}

export function PrerecordedSpeakerButton({
  voiceName,
  type,
  className = '',
  size = 'sm',
  variant = 'ghost',
  onClick
}: PrerecordedSpeakerButtonProps) {
  const { audioState, playPrerecordedAudio, stopAudio } = usePrerecordedAudio();

  const handleClick = async (e: React.MouseEvent) => {
    // Prevent event bubbling to parent elements
    if (onClick) {
      onClick(e);
    }

    if (audioState.isPlaying) {
      stopAudio();
    } else if (!audioState.isLoading) {
      await playPrerecordedAudio(voiceName, type);
    }
  };

  const getIcon = () => {
    if (audioState.isLoading) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    if (audioState.isPlaying) {
      return <VolumeX className="h-4 w-4" />;
    }
    return <Volume2 className="h-4 w-4" />;
  };

  const getTooltipText = () => {
    if (audioState.error) {
      return `Error: ${audioState.error}`;
    }
    if (audioState.isLoading) {
      return 'Loading audio...';
    }
    if (audioState.isPlaying) {
      return 'Stop audio';
    }
    return `Preview ${voiceName} voice`;
  };

  const isDisabled = audioState.isLoading;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            onClick={handleClick}
            disabled={isDisabled}
            className={`transition-all duration-200 ${
              audioState.isPlaying ? 'text-primary' : ''
            } ${audioState.error ? 'text-destructive' : ''} ${className}`}
            aria-label={getTooltipText()}
          >
            {getIcon()}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">{getTooltipText()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
