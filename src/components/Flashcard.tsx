"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from '@/lib/utils';
import { Repeat } from 'lucide-react';
import { LazyTTSButton } from '@/components/LazyTTSButton';
import { useTTSVoices } from '@/hooks/use-tts-voices';

interface FlashcardProps {
  question: string;
  answer: string;
  isFlipped: boolean;
  onFlip: () => void;
  onMarkKnown?: () => void;
  onMarkNotKnown?: () => void;
}

export const Flashcard = React.memo(function Flashcard({ question, answer, isFlipped, onFlip, onMarkKnown, onMarkNotKnown }: FlashcardProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [isTouched, setIsTouched] = useState(false);
  const [dragX, setDragX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const startXRef = useRef<number | null>(null);
  const startYRef = useRef<number | null>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  const isHorizontalSwipeRef = useRef<boolean>(false);

  const { questionVoice, answerVoice } = useTTSVoices();

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || '';
      const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
      setIsMobile(mobileRegex.test(userAgent.toLowerCase()));
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Simplified touch handlers - no preventDefault to avoid browser interventions
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!isMobile || !isFlipped) return;
    setIsTouched(true);
    setIsDragging(true);
    setDragX(0);
    startXRef.current = e.touches[0].clientX;
    startYRef.current = e.touches[0].clientY;
    isHorizontalSwipeRef.current = false;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isMobile || !isFlipped || !isDragging || startXRef.current === null || startYRef.current === null) return;

    const deltaX = e.touches[0].clientX - startXRef.current;
    const deltaY = e.touches[0].clientY - startYRef.current;

    // Only track horizontal movement if it's clearly more horizontal than vertical
    // Require at least 20px horizontal movement and more horizontal than vertical
    const isHorizontalGesture = Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 20;

    if (isHorizontalGesture) {
      // Track horizontal swipe without preventing scroll
      isHorizontalSwipeRef.current = true;
      setDragX(deltaX);
    } else if (!isHorizontalSwipeRef.current) {
      // Reset drag if it's clearly vertical movement
      setDragX(0);
    }
  };

  const handleTouchEnd = () => {
    if (!isMobile || !isFlipped || !isDragging) return;
    setIsTouched(false);
    setIsDragging(false);
    const threshold = 80; // px

    if (dragX > threshold) {
      // Swiped right - "Knew it"
      setDragX(500); // animate off-screen
      setTimeout(() => {
        setDragX(0);
        if (onMarkKnown) {
          onMarkKnown();
        } else {
          onFlip();
        }
      }, 200);
    } else if (dragX < -threshold) {
      // Swiped left - "Didn't know it"
      setDragX(-500); // animate off-screen
      setTimeout(() => {
        setDragX(0);
        if (onMarkNotKnown) {
          onMarkNotKnown();
        } else {
          onFlip();
        }
      }, 200);
    } else {
      // Snap back
      setDragX(0);
    }
    startXRef.current = null;
    startYRef.current = null;
    isHorizontalSwipeRef.current = false;
  };



  // Calculate rotation and color feedback for a nice effect
  const rotation = dragX / 20;
  const threshold = 80;
  const isNearThreshold = Math.abs(dragX) > threshold * 0.6; // Start showing feedback at 60% of threshold
  const isKnownDirection = dragX > 0;

  const cardStyle = isMobile && isFlipped
    ? {
        transform: `translateX(${dragX}px) rotate(${rotation}deg)`,
        transition: isDragging ? 'none' : 'transform 0.2s cubic-bezier(.22,1,.36,1)',
        backgroundColor: isDragging && isNearThreshold
          ? (isKnownDirection ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)')
          : undefined,
        borderColor: isDragging && isNearThreshold
          ? (isKnownDirection ? 'rgba(34, 197, 94, 0.3)' : 'rgba(239, 68, 68, 0.3)')
          : undefined,
      }
    : undefined;

  return (
    <div
      className={cn(
        "flashcard-flip-container w-full h-80 md:h-96 cursor-pointer group transition-transform mx-auto",
        isTouched && "scale-[0.98]"
      )}
      ref={cardRef}
      style={cardStyle}
      onClick={onFlip}
      onTouchStart={isMobile && isFlipped ? handleTouchStart : undefined}
      onTouchMove={isMobile && isFlipped ? handleTouchMove : undefined}
      onTouchEnd={isMobile && isFlipped ? handleTouchEnd : undefined}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && onFlip()}
      aria-pressed={isFlipped}
      aria-label={isFlipped ? `Answer: ${answer}` : `Question: ${question}. Click to flip.`}
    >
      <div className={cn("flashcard-flipper w-full h-full", isFlipped && "flipped")}>
        {/* Front of the card (Question) */}
        <Card className="flashcard-front absolute inset-0 flex flex-col justify-center items-center shadow-xl transition-shadow group-hover:shadow-2xl">
          <CardContent className="p-4 sm:p-6 w-full h-full flex flex-col justify-center items-center text-center relative">
            <ScrollArea className="h-full w-full flex items-center">
              <div className="flex items-center justify-center h-full w-full px-2">
                <p className="text-lg md:text-xl font-medium text-center text-card-foreground">
                  {question}
                </p>
              </div>
            </ScrollArea>

            <div className="mt-4 flex items-center justify-center text-xs text-muted-foreground">
              <Repeat className="h-3 w-3 mr-1 animate-pulse" />
              <span>{isMobile ? "Tap" : "Click"} to see answer</span>
            </div>

            {/* Speaker button for question - TTS is now universally available */}
            <div className="absolute bottom-4 right-4">
              <LazyTTSButton
                text={question}
                voiceName={questionVoice}
                className="opacity-70 hover:opacity-100"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              />
            </div>
          </CardContent>
        </Card>

        {/* Back of the card (Answer) */}
        <Card className="flashcard-back absolute inset-0 flex flex-col justify-center items-center shadow-xl transition-shadow group-hover:shadow-2xl">
          <CardContent className="p-4 sm:p-6 w-full h-full flex flex-col justify-center items-center text-center relative">
            <ScrollArea className="h-full w-full flex items-center">
              <div className="flex items-center justify-center h-full w-full px-2">
                <p className="text-base md:text-lg text-center text-card-foreground">
                  {answer}
                </p>
              </div>
            </ScrollArea>

            <div className="mt-4 flex items-center justify-center text-xs text-muted-foreground">
              <Repeat className="h-3 w-3 mr-1 animate-pulse" />
              <span>{isMobile ? "Tap" : "Click"} to see question</span>
            </div>

            {/* Speaker button for answer - TTS is now universally available */}
            <div className="absolute bottom-4 right-4">
              <LazyTTSButton
                text={answer}
                voiceName={answerVoice}
                className="opacity-70 hover:opacity-100"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
});
