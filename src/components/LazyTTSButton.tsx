"use client";

import React, { lazy, Suspense, memo } from 'react';
import { Button } from '@/components/ui/button';
import { Volume2 } from 'lucide-react';

// Lazy load the SpeakerButton component
const SpeakerButton = lazy(() => 
  import('./SpeakerButton').then(module => ({ default: module.SpeakerButton }))
);

interface LazyTTSButtonProps {
  text: string;
  voiceName?: string;
  className?: string;
  size?: 'sm' | 'lg' | 'default';
  variant?: 'ghost' | 'outline' | 'default';
  onClick?: (e: React.MouseEvent) => void;
}

// Fallback component while TTS is loading
const TTSFallback = memo(({ className }: { className?: string }) => (
  <Button
    variant="ghost"
    size="sm"
    disabled
    className={`opacity-50 ${className}`}
    aria-label="Loading speech..."
  >
    <Volume2 className="h-4 w-4" />
  </Button>
));

TTSFallback.displayName = 'TTSFallback';

export const LazyTTSButton = memo(({
  text,
  voiceName = 'Rachel',
  className = '',
  size = 'sm',
  variant = 'ghost',
  onClick
}: LazyTTSButtonProps) => {
  // Don't render TTS for very long text to avoid performance issues
  if (text.length > 1000) {
    return null;
  }

  return (
    <Suspense fallback={<TTSFallback className={className} />}>
      <SpeakerButton
        text={text}
        voiceName={voiceName}
        className={className}
        size={size}
        variant={variant}
        onClick={onClick}
      />
    </Suspense>
  );
});

LazyTTSButton.displayName = 'LazyTTSButton';
