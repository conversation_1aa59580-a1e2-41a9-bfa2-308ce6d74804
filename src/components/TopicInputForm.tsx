"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { BookOpen, AlertCircle, Loader2, Info, Lightbulb } from 'lucide-react';
import type { FlashcardData } from '@/types';
import { generateTopicFlashcardsAction } from '@/app/actions';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/lib/auth-context';
import { useUsageService } from '@/lib/usage-service';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useSettingsService } from '@/lib/settings-service';
import { logger } from '@/lib/logger';

interface TopicInputFormProps {
  onFlashcardsGenerated: (flashcards: FlashcardData[], title?: string) => void;
  setIsLoading: (isLoading: boolean) => void;
}

const SUBJECTS = [
  { value: 'general', label: 'General' },
  { value: 'science', label: 'Science' },
  { value: 'mathematics', label: 'Mathematics' },
  { value: 'history', label: 'History' },
  { value: 'literature', label: 'Literature' },
  { value: 'geography', label: 'Geography' },
  { value: 'economics', label: 'Economics' },
  { value: 'psychology', label: 'Psychology' },
  { value: 'computer science', label: 'Computer Science' },
  { value: 'biology', label: 'Biology' },
  { value: 'chemistry', label: 'Chemistry' },
  { value: 'physics', label: 'Physics' },
  { value: 'business', label: 'Business' },
];

const DIFFICULTIES = [
  { value: 'beginner', label: 'Beginner', description: 'Basic concepts and definitions' },
  { value: 'intermediate', label: 'Intermediate', description: 'Balanced coverage with applications' },
  { value: 'advanced', label: 'Advanced', description: 'Complex concepts and critical thinking' },
];

const TOPIC_SUGGESTIONS = [
  'Photosynthesis',
  'World War II',
  'Calculus derivatives',
  'Shakespeare\'s Hamlet',
  'Climate change',
  'Machine learning basics',
  'Human anatomy',
  'Economic supply and demand',
  'Chemical bonding',
  'Ancient Rome',
];

export function TopicInputForm({ onFlashcardsGenerated, setIsLoading: setAppIsLoading }: TopicInputFormProps) {
  const [topic, setTopic] = useState('');
  const [difficulty, setDifficulty] = useState<'beginner' | 'intermediate' | 'advanced'>('intermediate');
  const [subject, setSubject] = useState('general');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCheckingLimits, setIsCheckingLimits] = useState(false);
  const [usageInfo, setUsageInfo] = useState<{
    dailyUploads: number;
    monthlyUploads: number;
    dailyLimit: number;
    monthlyLimit: number;
    tier: string;
  } | null>(null);

  const { toast } = useToast();
  const { user, loading } = useAuth();
  const { checkLimits, trackUserUpload, LIMITS } = useUsageService();
  const router = useRouter();
  const { getUserSettings, DEFAULT_SETTINGS } = useSettingsService();

  // Check user limits when user state changes
  useEffect(() => {
    const checkUserLimits = async () => {
      if (!user || loading) return;

      setIsCheckingLimits(true);
      try {
        const limits = await checkLimits();
        if (limits.currentUsage) {
          const tier = limits.currentUsage.tier || 'FREE';
          const tierLimits = LIMITS[tier as keyof typeof LIMITS];

          setUsageInfo({
            dailyUploads: limits.currentUsage.dailyUploads,
            monthlyUploads: limits.currentUsage.monthlyUploads,
            dailyLimit: tierLimits.DAILY_UPLOADS,
            monthlyLimit: tierLimits.MONTHLY_UPLOADS,
            tier
          });
        }
      } catch (error) {
        logger.error('Error checking limits:', error);
      } finally {
        setIsCheckingLimits(false);
      }
    };

    checkUserLimits();
  }, [user, loading, checkLimits, LIMITS]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Check if user is logged in
    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please sign in to generate flashcards.",
      });
      router.push('/auth/signin');
      return;
    }

    // Check rate limits
    try {
      const limits = await checkLimits();
      if (!limits.canUpload) {
        setError(limits.reason || 'Rate limit exceeded');
        toast({
          variant: "destructive",
          title: "Rate Limit Exceeded",
          description: limits.reason || 'You have reached your usage limit',
        });
        return;
      }
    } catch (error) {
      logger.error('Error checking limits:', error);
    }

    if (!topic.trim()) {
      setError('Please enter a topic to generate flashcards about.');
      return;
    }

    if (topic.trim().length < 3) {
      setError('Topic must be at least 3 characters long.');
      return;
    }

    if (topic.trim().length > 500) {
      setError('Topic must be less than 500 characters.');
      return;
    }

    setIsSubmitting(true);
    setAppIsLoading(true);
    setError(null);

    try {
      // Get user settings
      const settings = await getUserSettings(user.id);

      // Generate flashcards from topic
      const result = await generateTopicFlashcardsAction({
        topic: topic.trim(),
        difficulty,
        subject,
        settings
      });

      if (result.success && result.data) {
        // Track successful generation for rate limiting
        await trackUserUpload();

        onFlashcardsGenerated(result.data.flashcards, result.data.title);
        toast({
          title: "Success!",
          description: `Generated ${result.data.flashcards.length} flashcards about "${topic}"`,
        });
      } else {
        throw new Error(result.error || 'Failed to generate flashcards');
      }
    } catch (error) {
      logger.error('Error generating topic flashcards:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate flashcards');
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to generate flashcards',
      });
    } finally {
      setIsSubmitting(false);
      setAppIsLoading(false);
    }
  };

  const handleTopicSuggestionClick = (suggestion: string) => {
    setTopic(suggestion);
    setError(null);
  };

  const renderAuthAlert = () => {
    if (!loading && !user) {
      return (
        <Alert className="mb-6 mt-1">
          <Info className="h-4 w-4" />
          <AlertTitle>Authentication Required</AlertTitle>
          <AlertDescription>
            <p>You need to <Link href="/auth/signin" className="font-medium underline">sign in</Link> to generate flashcards.</p>
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  };

  const renderUsageInfo = () => {
    if (user && usageInfo) {
      return (
        <div className="text-xs text-muted-foreground mt-2 flex flex-col gap-1">
          <p className="text-sm font-medium">{usageInfo.tier} Plan</p>
          <p>Daily: {usageInfo.dailyUploads} / {usageInfo.dailyLimit} generations used</p>
          <p>Monthly: {usageInfo.monthlyUploads} / {usageInfo.monthlyLimit} generations used</p>
          {process.env.NEXT_PUBLIC_ENABLE_SUBSCRIPTIONS === 'true' && (
            <Link href="/subscription" className="text-xs text-primary hover:underline mt-1">
              Upgrade plan
            </Link>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="w-full flex justify-center items-center">
      <div className="w-full max-w-md p-4 sm:p-8 space-y-6 bg-card text-card-foreground rounded-lg shadow-xl mx-auto">
        <div className="text-center">
          <h2 className="text-2xl font-semibold">Enter Topic</h2>
          <p className="text-sm text-muted-foreground">
            Describe what you want to study and we'll generate flashcards for you.
          </p>
        </div>

        {renderAuthAlert()}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <Label htmlFor="topic" className="text-sm font-medium">Topic or Subject</Label>
            <Textarea
              id="topic"
              placeholder="e.g., Photosynthesis, World War II, Calculus derivatives..."
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              className="mt-2 min-h-[100px]"
              disabled={isSubmitting || isCheckingLimits}
              maxLength={500}
            />
            <div className="flex justify-between items-center mt-1">
              <p className="text-xs text-muted-foreground">
                {topic.length}/500 characters
              </p>
            </div>
          </div>

          {/* Topic Suggestions */}
          <div>
            <Label className="text-sm font-medium flex items-center gap-1">
              <Lightbulb className="h-4 w-4" />
              Quick Suggestions
            </Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {TOPIC_SUGGESTIONS.map((suggestion) => (
                <Button
                  key={suggestion}
                  type="button"
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => handleTopicSuggestionClick(suggestion)}
                  disabled={isSubmitting || isCheckingLimits}
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="subject" className="text-sm font-medium">Subject Area</Label>
              <Select value={subject} onValueChange={setSubject} disabled={isSubmitting || isCheckingLimits}>
                <SelectTrigger className="mt-2">
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {SUBJECTS.map((subj) => (
                    <SelectItem key={subj.value} value={subj.value}>
                      {subj.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="difficulty" className="text-sm font-medium">Difficulty Level</Label>
              <Select value={difficulty} onValueChange={(value: 'beginner' | 'intermediate' | 'advanced') => setDifficulty(value)} disabled={isSubmitting || isCheckingLimits}>
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {DIFFICULTIES.map((diff) => (
                    <SelectItem key={diff.value} value={diff.value}>
                      <div>
                        <div className="font-medium">{diff.label}</div>
                        <div className="text-xs text-muted-foreground">{diff.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {renderUsageInfo()}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            type="submit"
            className="w-full py-6 text-base"
            disabled={isSubmitting || !topic.trim() || isCheckingLimits || (!user && !loading)}
            size="lg"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Generating...
              </>
            ) : isCheckingLimits ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Checking limits...
              </>
            ) : (
              <>
                <BookOpen className="mr-2 h-5 w-5" />
                Generate Flashcards
              </>
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
