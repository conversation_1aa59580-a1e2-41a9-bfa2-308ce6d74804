import { NextRequest, NextResponse } from 'next/server';
import { getUserTierServer } from '@/lib/firebase-admin';
import { generateSpeechElevenLabs, getAvailableVoiceNames } from '@/lib/elevenlabs-tts';
import { getUserElevenLabsApiKey } from '@/lib/settings-service-server';
import { logger } from '@/lib/logger';

interface TTSFallbackRequest {
  text: string;
  voiceName?: string;
  userId?: string;
}

interface TTSFallbackResponse {
  success: boolean;
  audioData?: string; // base64 encoded audio
  error?: string;
}

// Voice validation is now handled by the ElevenLabs service

/**
 * Fallback endpoint for TTS when cached URLs fail to load
 * This endpoint generates fresh audio data without caching
 */
export async function POST(request: NextRequest): Promise<NextResponse<TTSFallbackResponse>> {
  try {
    const body: TTSFallbackRequest = await request.json();
    const { text, voiceName = 'Rachel', userId } = body;

    logger.log('[TTS Fallback] Request received:', {
      textLength: text?.length,
      voiceName,
      userId,
      hasUserId: !!userId
    });

    // Validate input
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      logger.log('[TTS Fallback] Invalid text input');
      return NextResponse.json(
        { success: false, error: 'Text is required and must be a non-empty string' },
        { status: 400 }
      );
    }

    // Check user authentication and determine API key to use
    if (!userId) {
      logger.log('[TTS Fallback] No user ID provided');
      return NextResponse.json(
        { success: false, error: 'User authentication required' },
        { status: 401 }
      );
    }

    logger.log('[TTS Fallback] Checking user tier for userId:', userId);
    const userTier = await getUserTierServer(userId);
    logger.log('[TTS Fallback] User tier retrieved:', { userId, userTier, isPro: userTier === 'PRO' });

    // Determine API key to use based on user tier
    let apiKeyToUse: string | undefined;

    if (userTier === 'PRO') {
      // PRO users use the application's API key
      logger.log('[TTS Fallback] PRO user - using application API key');
      apiKeyToUse = undefined; // Will use default app key
    } else {
      // FREE/BASIC users must provide their own API key
      logger.log('[TTS Fallback] FREE/BASIC user - checking for user API key');
      const userApiKey = await getUserElevenLabsApiKey(userId);

      if (!userApiKey) {
        logger.log('[TTS Fallback] No user API key found for FREE/BASIC user');
        return NextResponse.json(
          { success: false, error: 'Please provide your ElevenLabs API key in settings to use text-to-speech' },
          { status: 403 }
        );
      }

      logger.log('[TTS Fallback] Using user-provided API key');
      apiKeyToUse = userApiKey;
    }

    // Validate text length
    if (text.length > 5000) {
      return NextResponse.json(
        { success: false, error: 'Text is too long. Please use shorter text (max 5000 characters)' },
        { status: 400 }
      );
    }

    // Validate voice name
    const availableVoices = getAvailableVoiceNames();
    if (!availableVoices.includes(voiceName)) {
      return NextResponse.json(
        { success: false, error: `Invalid voice name. Available voices: ${availableVoices.join(', ')}` },
        { status: 400 }
      );
    }

    logger.log('[TTS Fallback] Generating TTS for text:', text.substring(0, 100) + '...');
    logger.log('[TTS Fallback] Using voice:', voiceName);

    // Generate speech using ElevenLabs TTS
    const audioData = await generateSpeechElevenLabs(text, voiceName, apiKeyToUse);

    if (!audioData) {
      logger.error('[TTS Fallback] No audio data received from TTS model');
      return NextResponse.json(
        { success: false, error: 'Failed to generate audio. No audio data received from the model.' },
        { status: 500 }
      );
    }

    logger.log('[TTS Fallback] TTS generation successful, audio data length:', audioData.length);

    // Return only the base64 audio data (no caching for fallback)
    return NextResponse.json({
      success: true,
      audioData: audioData // Already base64 encoded from ElevenLabs
    });

  } catch (error) {
    logger.error('[TTS Fallback] Error in TTS generation:', error);

    let errorMessage = 'Failed to generate speech';
    if (error instanceof Error) {
      if (error.message.includes('quota')) {
        errorMessage = 'API quota exceeded. Please try again later.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Speech generation timed out. Please try again.';
      } else if (error.message.includes('model')) {
        errorMessage = 'TTS model is currently unavailable. Please try again later.';
      } else {
        errorMessage = `Speech generation failed: ${error.message}`;
      }
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
