import { NextResponse } from 'next/server';
import { db, doc, updateDoc, getDoc, setDoc, collection, query, where, getDocs, limit } from '@/lib/firebase';
import crypto from 'crypto';
import { logger } from '@/lib/logger';

// Function to verify Paystack webhook signature
function verifySignature(request: Request, body: string): boolean {
  try {
    const signature = request.headers.get('x-paystack-signature');
    const secret = process.env.PAYSTACK_WEBHOOK_SECRET;
    
    if (!signature || !secret) {
      return false;
    }

    const hash = crypto
      .createHmac('sha512', secret)
      .update(body)
      .digest('hex');
      
    return hash === signature;
  } catch (error) {
    logger.error('Error verifying webhook signature:', error);
    return false;
  }
}

export async function POST(request: Request) {
  try {
    // Get the raw body text
    const body = await request.text();
    
    // Verify the request signature
    if (!verifySignature(request, body)) {
      logger.error('Invalid webhook signature');
      return NextResponse.json({ status: false, message: 'Invalid signature' }, { status: 401 });
    }

    // Parse the body as JSON
    const event = JSON.parse(body);
    
    // Extract event type and data
    const { event: eventType, data } = event;
    logger.log(`Processing Paystack webhook event: ${eventType}`, JSON.stringify(data, null, 2));

    // Handle different event types
    switch (eventType) {
      case 'charge.success':
        await handleSuccessfulCharge(data);
        break;
        
      case 'subscription.create':
        logger.log('New subscription created:', data.subscription_code);
        await handleSubscriptionCreated(data);
        break;
        
      case 'subscription.disable':
        logger.log('Subscription disabled:', data.subscription_code);
        await handleSubscriptionDisabled(data);
        break;
        
      case 'subscription.not_renewing':
        logger.log('Subscription marked as not renewing:', data.subscription_code);
        await handleSubscriptionNotRenewing(data);
        break;
        
      default:
        logger.log(`Unhandled event type: ${eventType}`);
    }

    return NextResponse.json({ status: true, message: 'Webhook processed successfully' });
  } catch (error: any) {
    logger.error('Error processing webhook:', error);
    return NextResponse.json(
      { status: false, message: 'Failed to process webhook', error: error.message },
      { status: 500 }
    );
  }
}

// Handle successful charge event
async function handleSuccessfulCharge(data: any) {
  // Get customer email
  const { customer, plan } = data;
  
  if (!customer || !customer.email || !plan) {
    logger.log('Missing customer or plan data');
    return;
  }
  
  try {
    logger.log(`Processing subscription for plan code: ${plan.plan_code}`);
    
    // Determine the subscription tier based on the plan code
    let tier;
    // Match exact plan codes from our application
    if (plan.plan_code === 'PLN_exwlsxe2x9db6h3') {
      tier = 'BASIC';
    } else if (plan.plan_code === 'PLN_1uq5sio6o2yag1b') {
      tier = 'PRO';
    } else {
      logger.log(`Unknown plan code: ${plan.plan_code}`);
      return;
    }
    
    // Get user document by email
    const usersRef = collection(db, 'users');
    const userQuery = query(
      usersRef,
      where('email', '==', customer.email),
      limit(1)
    );
    const usersSnapshot = await getDocs(userQuery);
    
    if (usersSnapshot.empty) {
      logger.log(`User not found for email: ${customer.email}`);
      return;
    }
    
    const userDoc = usersSnapshot.docs[0];
    const userId = userDoc.id;
    
    // Update the user's usage data with the new tier
    const userUsageRef = doc(db, 'userUsage', userId);
    const userUsageDoc = await getDoc(userUsageRef);
    
    if (userUsageDoc.exists()) {
      // Update existing usage record
      await updateDoc(userUsageRef, {
        tier,
        lastUpdatedAt: new Date()
      });
    } else {
      // Create new usage record
      await setDoc(userUsageRef, {
        dailyUploads: 0,
        monthlyUploads: 0,
        lastUploadAt: null,
        lastResetAt: new Date(),
        tier,
        lastUpdatedAt: new Date()
      });
    }
    
    logger.log(`User ${userId} subscription updated to ${tier}`);
  } catch (error) {
    logger.error('Error handling charge.success event:', error);
    throw error;
  }
}

// Handle subscription created event
async function handleSubscriptionCreated(data: any) {
  // Similar to handleSuccessfulCharge but for subscription.create event
  await handleSuccessfulCharge(data);
}

// Handle subscription disabled event
async function handleSubscriptionDisabled(data: any) {
  const { customer } = data;
  
  if (!customer || !customer.email) {
    logger.log('Missing customer data');
    return;
  }
  
  try {
    // Get user document by email
    const usersRef = collection(db, 'users');
    const userQuery = query(
      usersRef,
      where('email', '==', customer.email),
      limit(1)
    );
    const usersSnapshot = await getDocs(userQuery);
    
    if (usersSnapshot.empty) {
      logger.log(`User not found for email: ${customer.email}`);
      return;
    }
    
    const userDoc = usersSnapshot.docs[0];
    const userId = userDoc.id;
    
    // Update the user's usage data to FREE tier
    const userUsageRef = doc(db, 'userUsage', userId);
    await updateDoc(userUsageRef, {
      tier: 'FREE',
      lastUpdatedAt: new Date()
    });
    
    logger.log(`User ${userId} subscription downgraded to FREE`);
  } catch (error) {
    logger.error('Error handling subscription.disable event:', error);
    throw error;
  }
}

// Handle subscription not renewing event
async function handleSubscriptionNotRenewing(data: any) {
  // This is similar to handleSubscriptionDisabled
  // We're marking the user for downgrade but not immediately downgrading
  const { customer } = data;
  
  if (!customer || !customer.email) {
    logger.log('Missing customer data');
    return;
  }
  
  try {
    // Get user document by email
    const usersRef = collection(db, 'users');
    const userQuery = query(
      usersRef,
      where('email', '==', customer.email),
      limit(1)
    );
    const usersSnapshot = await getDocs(userQuery);
    
    if (usersSnapshot.empty) {
      logger.log(`User not found for email: ${customer.email}`);
      return;
    }
    
    const userDoc = usersSnapshot.docs[0];
    const userId = userDoc.id;
    
    // Mark the user for downgrade
    const userUsageRef = doc(db, 'userUsage', userId);
    await updateDoc(userUsageRef, {
      willDowngrade: true,
      lastUpdatedAt: new Date()
    });
    
    logger.log(`User ${userId} marked for downgrade when subscription ends`);
  } catch (error) {
    logger.error('Error handling subscription.not_renewing event:', error);
    throw error;
  }
} 