import { NextRequest, NextResponse } from 'next/server';
import { getUserTierServer } from '@/lib/firebase-admin';
import {
  generateCacheKey,
  checkCacheExists,
  storeAudioInCache,
  shouldCacheText
} from '@/lib/tts-cache';
import { generateSpeechElevenLabs, getAvailableVoiceNames } from '@/lib/elevenlabs-tts';
import { getUserElevenLabsApiKey } from '@/lib/settings-service-server';
import { logger } from '@/lib/logger';

interface TTSRequest {
  text: string;
  voiceName?: string;
  userId?: string; // Add userId for subscription validation
}

interface TTSResponse {
  success: boolean;
  audioData?: string; // base64 encoded audio
  audioUrl?: string; // URL to cached audio file
  cached?: boolean; // indicates if audio was served from cache
  error?: string;
}

// Voice validation is now handled by the ElevenLabs service



export async function POST(request: NextRequest): Promise<NextResponse<TTSResponse>> {
  try {
    const body: TTSRequest = await request.json();
    const { text, voiceName = 'Rachel', userId } = body;

    logger.log('[TTS API] Request received:', {
      textLength: text?.length,
      voiceName,
      userId,
      hasUserId: !!userId
    });

    // Validate input
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      logger.log('[TTS API] Invalid text input');
      return NextResponse.json(
        { success: false, error: 'Text is required and must be a non-empty string' },
        { status: 400 }
      );
    }

    // Generate cache key and check cache (only if text is suitable for caching)
    let cacheKey: string | null = null;
    let shouldCache = false;

    try {
      shouldCache = shouldCacheText(text);
      if (shouldCache) {
        cacheKey = generateCacheKey(text, voiceName);
        logger.log('[TTS API] Generated cache key:', cacheKey);

        // Check if audio is already cached
        const cacheResult = await checkCacheExists(cacheKey);
        if (cacheResult.exists && cacheResult.url) {
          logger.log('🎯 [TTS API] ✅ CACHE HIT! Returning cached audio');
          logger.log('📁 [TTS API] Cache key:', cacheKey);
          logger.log('🔗 [TTS API] Cache URL:', cacheResult.url);
          logger.log('⚡ [TTS API] Skipping ElevenLabs API call - using cached audio');

          // For cached content, return the URL for direct access
          // Client will handle fallback if the URL fails to load
          return NextResponse.json({
            success: true,
            audioUrl: cacheResult.url,
            cached: true
          });
        } else {
          logger.log('❌ [TTS API] Cache miss for key:', cacheKey);
          logger.log('🔄 [TTS API] Will generate fresh audio via ElevenLabs API');
        }
      } else {
        logger.log('[TTS API] Text not suitable for caching, proceeding with TTS generation');
      }
    } catch (cacheError) {
      logger.warn('[TTS API] Cache operations failed, proceeding with TTS generation:', cacheError);
      // Continue with TTS generation if cache operations fail
      shouldCache = false;
      cacheKey = null;
    }

    // Determine API key to use based on user tier
    let apiKeyToUse: string | undefined;

    if (!userId) {
      logger.log('[TTS API] No userId provided, authentication required');
      return NextResponse.json(
        { success: false, error: 'User authentication required for text-to-speech' },
        { status: 401 }
      );
    }

    logger.log('[TTS API] Checking user tier for userId:', userId);
    try {
      const userTier = await getUserTierServer(userId);
      logger.log('[TTS API] User tier retrieved:', {
        userId,
        userTier,
        isPro: userTier === 'PRO'
      });

      if (userTier === 'PRO') {
        // PRO users use the application's API key
        logger.log('[TTS API] PRO user - using application API key');
        apiKeyToUse = undefined; // Will use default app key
      } else {
        // FREE/BASIC users must provide their own API key
        logger.log('[TTS API] FREE/BASIC user - checking for user API key');
        const userApiKey = await getUserElevenLabsApiKey(userId);

        if (!userApiKey) {
          logger.log('[TTS API] No user API key found for FREE/BASIC user');
          return NextResponse.json(
            { success: false, error: 'Please provide your ElevenLabs API key in settings to use text-to-speech' },
            { status: 403 }
          );
        }

        logger.log('[TTS API] Using user-provided API key');
        apiKeyToUse = userApiKey;
      }
    } catch (error) {
      logger.error('[TTS API] Error checking user tier or API key:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to verify subscription status' },
        { status: 500 }
      );
    }

    // Validate text length (TTS has a 32k token limit)
    if (text.length > 5000) { // Conservative limit to stay under token limit
      return NextResponse.json(
        { success: false, error: 'Text is too long. Please use shorter text (max 5000 characters)' },
        { status: 400 }
      );
    }

    // Validate voice name
    const availableVoices = getAvailableVoiceNames();
    if (!availableVoices.includes(voiceName)) {
      return NextResponse.json(
        { success: false, error: `Invalid voice name. Available voices: ${availableVoices.join(', ')}` },
        { status: 400 }
      );
    }

    logger.log('🤖 [TTS API] ⚡ GENERATING FRESH AUDIO via ElevenLabs API');
    logger.log('📝 [TTS API] Text preview:', text.substring(0, 100) + (text.length > 100 ? '...' : ''));
    logger.log('🎤 [TTS API] Voice:', voiceName);
    logger.log('📏 [TTS API] Text length:', text.length, 'characters');

    // Generate speech using ElevenLabs TTS
    logger.log('🌐 [TTS API] Making request to ElevenLabs TTS API...');

    const audioData = await generateSpeechElevenLabs(text, voiceName, apiKeyToUse);

    logger.log('✅ [TTS API] ElevenLabs API response received successfully');

    if (!audioData) {
      logger.error('❌ [TTS API] No audio data received from TTS model');
      return NextResponse.json(
        { success: false, error: 'Failed to generate audio. No audio data received from the model.' },
        { status: 500 }
      );
    }

    logger.log('🎵 [TTS API] ✅ FRESH AUDIO GENERATED! Audio data length:', audioData.length, 'characters');
    logger.log('💾 [TTS API] Audio size:', Math.round(audioData.length * 0.75 / 1024), 'KB (estimated)');

    // Cache the generated audio for future requests (only if suitable for caching)
    let newCachedUrl: string | undefined;
    if (shouldCache && cacheKey) {
      try {
        logger.log('💾 [TTS API] Caching fresh audio for future requests...');
        logger.log('🔑 [TTS API] Cache key:', cacheKey);
        newCachedUrl = await storeAudioInCache(cacheKey, audioData);
        logger.log('✅ [TTS API] ✨ AUDIO CACHED SUCCESSFULLY!');
        logger.log('🔗 [TTS API] Cache URL:', newCachedUrl);
        logger.log('⚡ [TTS API] Next request for same text+voice will be instant!');
      } catch (cacheError) {
        logger.warn('⚠️  [TTS API] Failed to cache audio, but continuing with response:', cacheError);
        // Don't fail the request if caching fails
      }
    } else {
      logger.log('⏭️  [TTS API] Skipping cache storage (shouldCache:', shouldCache, ', cacheKey:', !!cacheKey, ')');
    }

    return NextResponse.json({
      success: true,
      audioData: audioData, // Always include base64 data for new generations
      audioUrl: newCachedUrl, // Include cached URL if we just created one
      cached: false // This is a new generation
    });

  } catch (error) {
    logger.error('Error in TTS generation:', error);

    let errorMessage = 'Failed to generate speech';
    if (error instanceof Error) {
      if (error.message.includes('quota')) {
        errorMessage = 'API quota exceeded. Please try again later.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Speech generation timed out. Please try again.';
      } else if (error.message.includes('model')) {
        errorMessage = 'TTS model is currently unavailable. Please try again later.';
      } else {
        errorMessage = `Speech generation failed: ${error.message}`;
      }
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

// Optional: Add GET endpoint to list available voices
export async function GET(): Promise<NextResponse> {
  return NextResponse.json({
    availableVoices: getAvailableVoiceNames(),
    defaultVoice: 'Rachel'
  });
}
