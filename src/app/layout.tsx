import { defaultMetadata, viewport } from './metadata'
import { Providers } from '@/components/providers'
import { Analytics } from '@/components/analytics'
import { Toaster } from '@/components/ui/toaster'
import { OrganizationJsonLd, SoftwareApplicationJsonLd } from '@/components/json-ld'
import './globals.css'

export const metadata = defaultMetadata
export { viewport }

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/manifest.webmanifest" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="Flash Cards" />
        {/* iOS PWA keyboard fix - ensure proper input handling */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="format-detection" content="telephone=no" />
        {/* Additional iOS PWA meta tags for better compatibility */}
        <meta name="apple-touch-fullscreen" content="yes" />
        <meta name="apple-mobile-web-app-orientations" content="portrait" />
        <link rel="apple-touch-startup-image" href="/apple-touch-icon.png" />
        {/* Prevent iOS from styling form elements */}
        <meta name="disable-web-app-capable" content="no" />
        {/* PWA Service Worker Registration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js', {
                    scope: '/',
                    updateViaCache: 'none'
                  }).then(function(registration) {
                    console.log('SW registered: ', registration);
                  }).catch(function(registrationError) {
                    console.log('SW registration failed: ', registrationError);
                  });
                });
              }
            `,
          }}
        />
        <OrganizationJsonLd />
        <SoftwareApplicationJsonLd />
      </head>
      <body>
        <Providers>
          {children}
          <Analytics />
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
