"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SpeakerButton } from '@/components/SpeakerButton';
import { useSubscription } from '@/hooks/use-subscription';
import { useAuth } from '@/lib/auth-context';
import Link from 'next/link';

export default function TestTTSPage() {
  const { user } = useAuth();
  const { tier, hasTextToSpeech, requiresOwnApiKey, subscriptionsEnabled } = useSubscription();
  const [testText] = useState("Hello! This is a test of the text-to-speech functionality.");

  if (!user) {
    return (
      <main className="container mx-auto px-4 py-8 max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>TTS Test Page</CardTitle>
            <CardDescription>Please log in to test text-to-speech functionality</CardDescription>
          </CardHeader>
          <CardContent>
            <p>You need to be logged in to test the TTS features.</p>
          </CardContent>
        </Card>
      </main>
    );
  }

  return (
    <main className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">TTS Test Page</h1>
          <p className="text-muted-foreground">Test the new text-to-speech subscription model</p>
        </div>

        {/* User Info */}
        <Card>
          <CardHeader>
            <CardTitle>Your Account Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p><strong>User ID:</strong> {user.id}</p>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Subscription Tier:</strong> {tier}</p>
            <p><strong>Subscriptions Enabled:</strong> {subscriptionsEnabled ? 'Yes' : 'No'}</p>
            <p><strong>Has TTS Access:</strong> {hasTextToSpeech ? 'Yes' : 'No'}</p>
            <p><strong>Requires Own API Key:</strong> {requiresOwnApiKey ? 'Yes' : 'No'}</p>
          </CardContent>
        </Card>

        {/* TTS Test */}
        <Card>
          <CardHeader>
            <CardTitle>Text-to-Speech Test</CardTitle>
            <CardDescription>
              {requiresOwnApiKey
                ? "You can use TTS with your own ElevenLabs API key. Configure it in settings first."
                : "You can use TTS with the application's API key."
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-muted rounded-lg">
              <p className="text-sm mb-2"><strong>Test Text:</strong></p>
              <p>"{testText}"</p>
            </div>
            
            <div className="flex items-center gap-4">
              <SpeakerButton
                text={testText}
                voiceName="Rachel"
                size="default"
                variant="outline"
              />
              <span className="text-sm text-muted-foreground">
                Click to test TTS with Rachel voice
              </span>
            </div>

            {requiresOwnApiKey && (
              <div className="p-4 bg-secondary border border-border rounded-lg">
                <p className="text-sm text-secondary-foreground">
                  <strong>Note:</strong> As a {tier} user, you need to provide your own ElevenLabs API key.
                  Go to <Link href="/settings" className="text-primary hover:text-primary/80 underline">Settings</Link> to configure it.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex gap-4">
          <Button asChild variant="outline">
            <Link href="/settings">Go to Settings</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/elevenlabs-setup">Setup Guide</Link>
          </Button>
          <Button asChild>
            <Link href="/">Back to Home</Link>
          </Button>
        </div>
      </div>
    </main>
  );
}
