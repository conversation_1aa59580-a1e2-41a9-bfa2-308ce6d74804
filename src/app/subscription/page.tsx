"use client";

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/lib/auth-context';
import { TierManagement } from '@/components/ui/TierManagement';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';

export default function SubscriptionPage() {
  const { user, loading } = useAuth();

  return (
    <div className="container px-4 py-8 mx-auto max-w-5xl">
      <div className="mb-8 flex items-center">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/" className="flex items-center">
            <ChevronLeft className="mr-1 h-4 w-4" />
            Back to Home
          </Link>
        </Button>
      </div>
      
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold tracking-tight">Subscription Plans</h1>
        <p className="text-muted-foreground mt-2">
          Choose the plan that works best for you
        </p>
      </div>
      
      <TierManagement />
      
      <div className="mt-12 text-center text-sm text-muted-foreground">
        <p>Need more information? Contact <NAME_EMAIL></p>
      </div>
    </div>
  );
} 