import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowLeft, ExternalLink } from 'lucide-react'

export default function PrivacyPolicyPage() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Link href="/">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to App
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Privacy Policy</h1>
        <p className="text-muted-foreground">
          Last Updated: {new Date().toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </p>
      </div>

      {/* Notice */}
      <div className="bg-card p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-3">Important Notice</h2>
        <p className="text-muted-foreground mb-4">
          This Flash Cards AI application is developed and operated by Anker Studios. 
          Our privacy practices are governed by the comprehensive Anker Studios Privacy Policy, 
          with additional provisions specific to this application outlined below.
        </p>
        <Link 
          href="https://www.ankerstudios.co.za/legal-billing/privacy" 
          target="_blank" 
          rel="noopener noreferrer"
          className="inline-flex items-center text-primary hover:underline"
        >
          View Full Anker Studios Privacy Policy
          <ExternalLink className="w-4 h-4 ml-1" />
        </Link>
      </div>

      {/* Application-Specific Provisions */}
      <div className="space-y-6">
        <section>
          <h2 className="text-2xl font-semibold mb-4">Application-Specific Data Processing</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Document Processing and File Handling</h3>
              <p className="text-muted-foreground mb-2">
                When you upload documents to generate flashcards, we process them as follows:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1 mb-2">
                <li><strong>Supported formats:</strong> PDF, Microsoft Word (.docx/.doc), PowerPoint (.pptx/.ppt), plain text (.txt), and Markdown (.md) files</li>
                <li><strong>File size limit:</strong> Maximum 15MB per file</li>
                <li><strong>Temporary storage:</strong> Files are temporarily uploaded to Vercel Blob storage for processing</li>
                <li><strong>AI processing:</strong> Content is extracted and processed using Google Gemini AI to generate flashcards</li>
                <li><strong>Immediate deletion:</strong> All uploaded files are automatically deleted from our servers immediately after processing completion</li>
              </ul>
              <p className="text-muted-foreground">
                We do not retain copies of your uploaded documents beyond the processing session.
                Only the generated flashcard content (questions and answers) is stored in your account.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">AI Service Integration</h3>
              <p className="text-muted-foreground">
                We use Google Gemini AI (specifically Gemini 2.0 Flash model) for flashcard generation and
                ElevenLabs (Flash v2.5 model) for text-to-speech functionality. Your content may be processed
                by these third-party AI services in accordance with their respective privacy policies and our
                data processing agreements.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Text-to-Speech and API Key Storage</h3>
              <p className="text-muted-foreground mb-2">
                Our text-to-speech functionality operates differently based on your subscription tier:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1 mb-2">
                <li><strong>PRO users:</strong> We process your flashcard text using our ElevenLabs API integration</li>
                <li><strong>FREE/BASIC users:</strong> You may provide your own ElevenLabs API key, which we encrypt using AES-256-CBC encryption and store securely in Firebase Firestore</li>
              </ul>
              <p className="text-muted-foreground">
                User-provided API keys are never stored in plain text and are only decrypted server-side when needed for TTS generation.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Audio Caching and Performance</h3>
              <p className="text-muted-foreground">
                Generated audio content is cached in Vercel Blob storage using deterministic cache keys based on
                text content and voice settings. This caching improves performance and reduces API costs.
                Cached audio files are automatically cleaned up after 30 days. We do not record or store
                your voice or any audio input from your device.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Export Functionality</h3>
              <p className="text-muted-foreground">
                When you export flashcards as PDF or JSON files, this processing occurs on our servers using
                Puppeteer/Chromium for PDF generation. The exported files are generated on-demand and are not
                permanently stored. JSON exports are generated client-side in your browser.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Third-Party Service Integrations</h3>
              <p className="text-muted-foreground mb-2">
                We integrate with the following third-party services to provide our functionality:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1">
                <li><strong>Google Gemini AI:</strong> Document processing and flashcard generation</li>
                <li><strong>ElevenLabs:</strong> Text-to-speech audio generation</li>
                <li><strong>Firebase (Google):</strong> User authentication, database storage, and user management</li>
                <li><strong>Vercel Blob:</strong> Temporary file storage and audio caching</li>
                <li><strong>Paystack:</strong> Payment processing for subscriptions</li>
                <li><strong>Google Analytics:</strong> Website usage analytics (when enabled)</li>
              </ul>
              <p className="text-muted-foreground mt-2">
                Data shared with these services is processed in accordance with their respective privacy policies
                and our data processing agreements.
              </p>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Data Retention</h2>
          <div className="space-y-3">
            <p className="text-muted-foreground">
              Your flashcard sets and study history are retained in your account for as long as you maintain
              an active account. You can delete individual flashcard sets or your entire account at any time
              through the application interface.
            </p>
            <div className="text-muted-foreground">
              <p className="font-medium mb-2">Specific retention periods:</p>
              <ul className="list-disc list-inside space-y-1">
                <li><strong>Uploaded files:</strong> Deleted immediately after processing (within minutes)</li>
                <li><strong>Generated flashcards:</strong> Retained until manually deleted by user or account closure</li>
                <li><strong>Usage tracking data:</strong> Retained for billing and rate limiting purposes</li>
                <li><strong>Cached audio files:</strong> Automatically deleted after 30 days</li>
                <li><strong>User settings and API keys:</strong> Retained until account deletion</li>
              </ul>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Your Rights</h2>
          <p className="text-muted-foreground">
            You have the right to access, modify, or delete your personal information and flashcard data. 
            You can exercise these rights through your account settings or by contacting us directly.
          </p>
        </section>
      </div>

      {/* Contact Information */}
      <div className="bg-muted p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Contact Us</h2>
        <p className="text-muted-foreground mb-2">
          For privacy-related questions or to exercise your rights, please contact:
        </p>
        <div className="space-y-1 text-sm">
          <p><strong>Anker Studios</strong></p>
          <p>Email: <EMAIL></p>
          <p>Website: www.ankerstudios.co.za</p>
        </div>
      </div>
    </div>
  )
}
