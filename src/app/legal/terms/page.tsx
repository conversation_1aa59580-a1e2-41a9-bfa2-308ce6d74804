import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft, ExternalLink } from 'lucide-react'

export default function TermsPage() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Link href="/">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to App
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Terms and Conditions</h1>
        <p className="text-muted-foreground">
          Last Updated: {new Date().toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </p>
      </div>

      {/* Notice */}
      <div className="bg-card p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-3">Important Notice</h2>
        <p className="text-muted-foreground mb-4">
          This Flash Cards AI application is developed and operated by Anker Studios. 
          Your use of this application is governed by the comprehensive Anker Studios Terms and Conditions, 
          with additional provisions specific to this application outlined below.
        </p>
        <Link 
          href="https://www.ankerstudios.co.za/legal-billing/terms" 
          target="_blank" 
          rel="noopener noreferrer"
          className="inline-flex items-center text-primary hover:underline"
        >
          View Full Anker Studios Terms and Conditions
          <ExternalLink className="w-4 h-4 ml-1" />
        </Link>
      </div>

      {/* Application-Specific Terms */}
      <div className="space-y-6">
        <section>
          <h2 className="text-2xl font-semibold mb-4">Service Description</h2>
          <p className="text-muted-foreground">
            Flash Cards AI is an AI-powered flashcard generation service that allows you to:
          </p>
          <ul className="list-disc list-inside text-muted-foreground mt-2 space-y-1">
            <li>Upload documents (PDF, Word, PowerPoint, text, and Markdown files up to 15MB each) for flashcard generation</li>
            <li>Generate flashcards from topics using Google Gemini AI</li>
            <li>Use text-to-speech functionality powered by ElevenLabs for audio learning</li>
            <li>Export flashcards in PDF (printable) and JSON (data) formats</li>
            <li>Access subscription-based features with usage limits based on your tier</li>
            <li>Store and manage your flashcard collections in your account</li>
          </ul>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Acceptable Use</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Content Guidelines</h3>
              <p className="text-muted-foreground">
                You may only upload content that you own or have the right to use.
                Do not upload copyrighted materials, inappropriate content, or content that violates
                any applicable laws or regulations. Uploaded files must not exceed 15MB per file and
                must be in supported formats (PDF, Word, PowerPoint, text, or Markdown).
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Usage Limits and Fair Use</h3>
              <p className="text-muted-foreground">
                Your usage is subject to daily and monthly upload limits based on your subscription tier.
                These limits are designed to ensure fair access to our AI processing resources.
                Attempts to circumvent usage limits or abuse the service may result in account suspension.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">AI Service Usage</h3>
              <p className="text-muted-foreground">
                Our AI-powered features (Google Gemini for flashcard generation, ElevenLabs for text-to-speech)
                are provided "as is" and may not always produce perfect results. You are responsible for
                reviewing and verifying the accuracy of generated flashcards before using them for study purposes.
              </p>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Subscription Terms</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Service Tiers</h3>
              <ul className="list-disc list-inside text-muted-foreground space-y-1">
                <li><strong>Free:</strong> Up to 3 daily uploads (15 monthly), text-to-speech with own ElevenLabs API key</li>
                <li><strong>Basic:</strong> Up to 10 daily uploads (50 monthly), text-to-speech with own ElevenLabs API key</li>
                <li><strong>Pro:</strong> Up to 30 daily uploads (200 monthly), text-to-speech with app API key, priority processing, and export features</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">API Key Requirements</h3>
              <p className="text-muted-foreground">
                FREE and BASIC users must provide their own ElevenLabs API key for text-to-speech features.
                PRO users can use text-to-speech features with our application's API key.
                User-provided API keys are encrypted using AES-256-CBC encryption and stored securely.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Payment and Billing</h3>
              <p className="text-muted-foreground">
                Subscription fees are processed through secure third-party payment providers (Paystack).
                Subscriptions automatically renew unless cancelled before the renewal date.
              </p>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Data Processing and File Handling</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">File Processing</h3>
              <p className="text-muted-foreground">
                When you upload files for flashcard generation, they are temporarily stored in Vercel Blob storage,
                processed using Google Gemini AI, and then immediately deleted. We do not retain copies of your
                uploaded documents beyond the processing session.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Generated Content Storage</h3>
              <p className="text-muted-foreground">
                Flashcards generated from your uploads are stored in Firebase Firestore and associated with your account.
                You can delete individual flashcard sets or your entire account data at any time through the application interface.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Audio Caching</h3>
              <p className="text-muted-foreground">
                Text-to-speech audio is cached in Vercel Blob storage to improve performance and reduce costs.
                Cached audio files are automatically cleaned up after 30 days.
              </p>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Intellectual Property</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Your Content</h3>
              <p className="text-muted-foreground">
                You retain ownership of the content you upload. By using our service, you grant us 
                a limited license to process your content for the purpose of generating flashcards 
                and providing the service features.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Generated Content</h3>
              <p className="text-muted-foreground">
                Flashcards generated by our AI service are provided for your personal use. 
                While you can use and export these flashcards, the underlying AI technology 
                and service remain the property of Anker Studios and our technology partners.
              </p>
            </div>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-4">Service Availability</h2>
          <p className="text-muted-foreground">
            We strive to maintain high service availability but cannot guarantee uninterrupted access. 
            The service may be temporarily unavailable due to maintenance, updates, or technical issues. 
            We reserve the right to modify or discontinue features with reasonable notice.
          </p>
        </section>
      </div>

      {/* Contact Information */}
      <div className="bg-muted p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Contact Us</h2>
        <p className="text-muted-foreground mb-2">
          For questions about these terms or our service, please contact:
        </p>
        <div className="space-y-1 text-sm">
          <p><strong>Anker Studios</strong></p>
          <p>Email: <EMAIL></p>
          <p>Website: www.ankerstudios.co.za</p>
        </div>
      </div>
    </div>
  )
}
