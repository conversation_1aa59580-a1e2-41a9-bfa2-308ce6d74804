# Flashcard AI

A Next.js application that uses Gemini AI to generate flashcards from various document formats with advanced features including text-to-speech functionality.

## ✨ Features

- **AI-Powered Flashcard Generation**: Convert documents into comprehensive flashcards using Gemini AI
- **Text-to-Speech**: Listen to flashcard questions and answers with ElevenLabs' high-quality speech generation
- **Multiple File Formats**: Support for PDF, Word, PowerPoint, text, and markdown files
- **Export Options**: Export flashcards as PDF (printable) or JSON (data)
- **User Authentication**: Firebase-based user accounts and flashcard history
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Customizable Settings**: Adjust answer detail level and card count ranges

## 📁 Supported File Types

- PDF (.pdf)
- Microsoft Word (.docx, .doc)
- Microsoft PowerPoint (.pptx, .ppt)
- Plain Text (.txt)
- Markdown (.md)

## 🔊 Text-to-Speech

The application includes advanced text-to-speech functionality powered by ElevenLabs' high-quality speech generation:

- **Speaker buttons** on each flashcard (question and answer sides)
- **15 different voice options** with various tones and styles
- **Ultra-fast generation** using ElevenLabs Flash v2.5 model (75ms latency)
- **High-quality audio** generated server-side for security
- **Seamless playback** with loading states and error handling
- **One-click audio** - stop previous audio when starting new playback
- **Intelligent caching** - audio files are cached in Vercel Blob storage for improved performance
- **Cost-efficient** - 50% lower cost per character with Flash v2.5

### TTS Caching System

The TTS feature includes a sophisticated caching mechanism that:

- **Reduces API calls** by storing generated audio files in Vercel Blob storage
- **Improves performance** with near-instant playback for cached content
- **Uses deterministic cache keys** based on text content and voice selection
- **Automatically manages cache lifecycle** with 30-day expiration
- **Handles cache failures gracefully** with fallback to real-time generation
- **Filters sensitive content** to prevent caching of potentially sensitive information

### Available Voices

The TTS system supports 15 different voices including:
- **Rachel** (Calm female, American accent), **Adam** (Deep male, American accent), **George** (Raspy male, British accent)
- **Sarah** (Soft female, American accent), **Domi** (Strong female, American accent), **Josh** (Deep male, American accent)
- **Charlotte** (Seductive female, English-Swedish accent), **Charlie** (Casual male, Australian accent)
- **Fin** (Old male, Irish accent), **Dave** (Young male, British-Essex accent), **Dorothy** (Pleasant female, British accent)
- **Matilda** (Warm female, American accent), **Daniel** (Deep male, British accent), **Emily** (Calm female, American accent)
- **Antoni** (Well-rounded male, American accent) - all powered by ElevenLabs' premium voice technology

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd flashcard-ai
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Copy `.env.example` to `.env.local` and fill in your values:
   ```bash
   cp .env.example .env.local
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:9002](http://localhost:9002)

## 🔧 Environment Variables

### Required Variables

```bash
# Google AI API Key (required for flashcard generation)
GEMINI_API_KEY=your_google_ai_api_key_here

# ElevenLabs API Key (required for text-to-speech)
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Vercel Blob Storage (required for file uploads and TTS caching)
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token_here
```

### Model Configuration

```bash
# Gemini Model for flashcard generation
GEMINI_MODEL=googleai/gemini-2.0-flash
```

### Firebase Configuration (for user accounts)

```bash
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
# ... other Firebase config variables
```

### Currency Conversion

The application automatically displays USD prices alongside ZAR prices using exchangerate-api.com v6. The API key is currently embedded in the service for reliable exchange rate fetching with higher rate limits than the free tier.

See `.env.example` for the complete list of environment variables.

## 🏗️ Architecture

### AI Integration
- **Genkit Framework**: Google's Genkit for AI model integration
- **Gemini 2.0 Flash**: Primary model for flashcard generation
- **ElevenLabs Flash v2.5**: Ultra-fast text-to-speech with 75ms latency
- **Dynamic Prompting**: User settings control answer detail and card count

### Backend Services
- **Next.js API Routes**: Server-side processing for security
- **Firebase Firestore**: User data and flashcard history storage
- **Vercel Blob**: Secure file upload and storage
- **REST API**: Direct integration with ElevenLabs TTS API

### Frontend Components
- **React 18**: Modern React with hooks and context
- **Tailwind CSS**: Utility-first styling
- **Radix UI**: Accessible component primitives
- **Custom Audio Handling**: Web Audio API integration

## 📱 Usage

1. **Upload a document** using the drag-and-drop interface
2. **Wait for AI processing** - flashcards are generated automatically
3. **Review flashcards** using the flip interface
4. **Listen to content** using the speaker buttons
5. **Export or save** your flashcard sets
6. **Access history** to review previous sets

## 🎛️ Customization

### User Settings
- **Answer Detail Level**: Control how detailed the answers should be (0-100 scale)
- **Card Count Range**: Set minimum and maximum number of cards to generate
- **Voice Selection**: Different voices for questions vs answers

### Voice Customization
Questions use the "Kore" voice (firm tone) while answers use "Puck" (upbeat tone) by default, but this can be customized in the component props.

## 🗄️ Cache Management

The TTS caching system uses Vercel Cron Jobs for automated maintenance:

### Automated Cleanup
- **Cron Schedule**: Runs every 3 days at 2:00 AM UTC (`0 2 */3 * *`)
- **Automatic Deletion**: Removes cache files older than 30 days
- **Zero Maintenance**: No manual intervention required
- **Secure**: Only accessible via Vercel's cron job system

### Manual Testing
For development and testing purposes:
```bash
# Get cache statistics
GET /api/tts-cache-stats

# Trigger manual cleanup
POST /api/cron/tts-cache-cleanup
```

### Testing Cache Performance
Use the included test script to verify caching functionality:
```bash
node scripts/test-tts-cache.js
```

### Cron Job Configuration
The cron job is configured in `vercel.json`:
```json
{
  "crons": [
    {
      "path": "/api/cron/tts-cache-cleanup",
      "schedule": "0 2 */3 * *"
    }
  ]
}
```

## 🔒 Security

- **Server-side AI processing**: API keys never exposed to client
- **Firebase Authentication**: Secure user management
- **Input validation**: Comprehensive validation for all user inputs
- **Rate limiting**: Built-in protection against abuse

## 📄 License

This project is licensed under the MIT License.
