/**
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Helpers for constructing routing headers.
 *
 * These headers are used by Google infrastructure to determine how to route
 * requests, especially for services that are regional.
 *
 * Generally, these headers are specified as gRPC metadata.
 */
/**
 * Constructs the routing header from the given params
 *
 * @param {Object} params - the request header parameters.
 * @return {string} the routing header value.
 */
export declare function fromParams(params: {
    [index: string]: string | number | boolean;
}): string;
