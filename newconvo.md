# Flash Project Context

## Project Overview
Flash is a Next.js 15.2.3 application integrated with Firebase, featuring a modern tech stack and various API integrations including Paystack for payments. The project appears to be a web application with authentication, subscription management, and history tracking capabilities.

## Tech Stack
- **Framework**: Next.js 15.2.3
- **Runtime**: React 18.3.1
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom animations
- **UI Components**: Radix UI primitives
- **Backend Integration**: Firebase & Firebase Admin
- **State Management**: TanStack Query (React Query)
- **Form Handling**: React Hook Form with Zod validation
- **Payment Processing**: Paystack integration
- **AI Integration**: GenKit AI with Google AI support

## Project Structure
- `/src` - Main source code
  - `/ai` - AI-related functionality and flows
  - `/app` - Next.js app router pages and API routes
  - `/components` - Reusable React components
  - `/hooks` - Custom React hooks
  - `/lib` - Utility functions and shared logic
  - `/types` - TypeScript type definitions

## Key Features
1. Authentication (signin/signup)
2. Subscription management with Paystack
3. History tracking
4. API integrations
   - Paystack payment processing
   - Transaction verification
   - Subscription management
   - Webhook handling

## Development Environment
- Development server runs on port 9002 (configured in package.json)
- Uses TurboPack for faster development builds
- Includes TypeScript checking and linting
- Firebase configuration for backend services

## Important Endpoints
- `/api/paystack/*` - Payment processing endpoints
- `/auth/*` - Authentication routes
- `/subscription/*` - Subscription management
- `/history/*` - History tracking

## Configuration Files
- `.env` - Environment variables
- `firebase.json` - Firebase configuration
- `firestore.rules` - Firestore security rules
- `next.config.ts` - Next.js configuration
- `tailwind.config.ts` - Tailwind CSS configuration

## Getting Started
1. The project uses npm for package management
2. Development server can be started with `npm run dev`
3. Firebase and Paystack credentials need to be configured
4. Environment variables need to be set up in `.env`

## Notes
- The project uses a modern app router structure (Next.js 13+ file-based routing)
- Implements server-side rendering capabilities
- Uses advanced UI components from Radix UI
- Includes data visualization capabilities through Recharts
- Firebase Studio integration for backend services

This context should be referenced at the start of any new conversation about the project to ensure proper understanding of the codebase and its capabilities. 