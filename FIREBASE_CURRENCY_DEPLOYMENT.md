# Firebase Cloud Function Currency System - Deployment Guide

## Overview

This guide covers the deployment of the Firebase Cloud Function-based currency conversion system that fetches exchange rates daily and caches them in Firestore for improved performance and reliability.

## Architecture Summary

```
┌─────────────────┐    Daily     ┌──────────────────┐
│ ExchangeRate    │◄─────────────│ Firebase Cloud   │
│ API (v6)        │   00:00 UTC  │ Function         │
└─────────────────┘              └──────────────────┘
                                           │
                                           ▼
                                 ┌──────────────────┐
                                 │ Firestore        │
                                 │ /exchangeRates/  │
                                 │ current          │
                                 └──────────────────┘
                                           │
                                           ▼
┌─────────────────┐              ┌──────────────────┐
│ Client-Side     │◄─────────────│ Currency Service │
│ Components      │   Cached     │ (1hr cache)      │
└─────────────────┘   Rates      └──────────────────┘
```

## Deployment Steps

### 1. Deploy Firestore Security Rules

```bash
# Deploy updated security rules
firebase deploy --only firestore:rules
```

**Verify Rules:**
- Exchange rates collection is readable by authenticated users
- Exchange rates collection is only writable by Cloud Functions
- Existing user data rules remain intact

### 2. Build and Deploy Cloud Functions

```bash
# Navigate to functions directory
cd functions

# Install dependencies (if not already done)
npm install

# Build the TypeScript code
npm run build

# Deploy the functions
firebase deploy --only functions

# Or deploy specific functions
firebase deploy --only functions:fetchDailyExchangeRates,functions:fetchExchangeRatesManual
```

### 3. Test the Deployment

#### Manual Trigger Test
```bash
# Get your project region and ID
firebase projects:list

# Test the manual trigger function
curl https://[region]-[project-id].cloudfunctions.net/fetchExchangeRatesManual
```

Expected response:
```json
{
  "success": true,
  "data": {
    "zarToUsdRate": 0.055777,
    "usdToZarRate": 17.9286,
    "source": "api",
    "lastUpdated": "2025-06-24T12:00:00.000Z"
  },
  "message": "Exchange rates updated successfully"
}
```

#### Verify Firestore Data
1. Open Firebase Console
2. Navigate to Firestore Database
3. Check `/exchangeRates/current` document
4. Verify data structure matches expected format

#### Monitor Function Logs
```bash
# View logs for the scheduled function
firebase functions:log --only fetchDailyExchangeRates

# View logs for manual trigger
firebase functions:log --only fetchExchangeRatesManual
```

### 4. Test Client-Side Integration

The client-side currency service should automatically start using the Firestore-cached exchange rates. Test by:

1. **Loading Subscription Page**: Visit `/subscription` and verify USD prices display
2. **Check Browser Console**: Look for currency service logs
3. **Verify Fallback**: Temporarily disable Firestore access to test fallback rates

## Monitoring and Maintenance

### Daily Schedule Verification

The `fetchDailyExchangeRates` function runs daily at 00:00 UTC. Monitor its execution:

```bash
# Check recent executions
firebase functions:log --only fetchDailyExchangeRates --limit 10

# Check for errors
firebase functions:log --only fetchDailyExchangeRates | grep ERROR
```

### Exchange Rate Data Health

Monitor the freshness of exchange rate data:

1. **Firestore Console**: Check `lastUpdated` timestamp in `/exchangeRates/current`
2. **Client Logs**: Monitor for "stale data" warnings in browser console
3. **API Status**: Verify ExchangeRate-API service status if issues occur

### Cost Optimization

The new architecture provides significant cost benefits:

- **Before**: Multiple API calls per user session
- **After**: Single daily API call + Firestore reads
- **Savings**: ~95% reduction in external API usage

### Troubleshooting

#### Function Not Executing
```bash
# Check function deployment status
firebase functions:list

# Verify schedule configuration
firebase functions:config:get
```

#### API Rate Limits
- Monitor ExchangeRate-API usage in their dashboard
- Current plan allows sufficient calls for daily updates
- Fallback mechanism activates if limits exceeded

#### Firestore Permission Issues
```bash
# Test security rules
firebase firestore:rules:test

# Verify user authentication in client app
```

## Rollback Plan

If issues occur, you can quickly rollback:

### 1. Revert Client-Side Code
```bash
# Restore previous currency-service.ts from git
git checkout HEAD~1 -- src/lib/currency-service.ts
```

### 2. Disable Cloud Function
```bash
# Delete the scheduled function (keeps manual trigger)
firebase functions:delete fetchDailyExchangeRates
```

### 3. Emergency Fallback
The client-side service automatically falls back to static rates if Firestore is unavailable, ensuring the feature continues working.

## Success Metrics

After deployment, monitor these metrics:

1. **Performance**: Faster currency conversion loading times
2. **Reliability**: Reduced "conversion failed" errors
3. **Cost**: Lower ExchangeRate-API usage
4. **User Experience**: Consistent USD pricing display

## Next Steps

Consider these future enhancements:

1. **Multiple Currencies**: Extend to EUR, GBP, etc.
2. **Historical Rates**: Store rate history for analytics
3. **Rate Alerts**: Notify on significant rate changes
4. **Admin Dashboard**: Monitor exchange rate system health

The Firebase Cloud Function architecture provides a robust, scalable foundation for these future improvements.
