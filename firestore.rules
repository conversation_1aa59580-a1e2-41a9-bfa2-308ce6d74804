rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if user is accessing their own data
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Users collection rules
    match /users/{userId} {
      // Allow users to read and write their own documents
      allow read, write: if isOwner(userId);
      
      // Allow creation of user document if authenticated and creating own document
      allow create: if isAuthenticated() && request.auth.uid == userId;

      // Validate settings structure
      function isValidSettings() {
        let settings = request.resource.data.settings;
        return settings == null || (
          settings.answerDetail is number &&
          settings.answerDetail >= 0 &&
          settings.answerDetail <= 100 &&
          settings.cardRange is map &&
          settings.cardRange.min is number &&
          settings.cardRange.max is number &&
          settings.cardRange.min >= 3 &&
          settings.cardRange.max <= 100 &&
          settings.cardRange.min <= settings.cardRange.max
        );
      }

      // Only allow updates that maintain valid settings structure
      allow update: if isOwner(userId) && isValidSettings();
    }

    // Flashcard sets collection rules (existing rules)
    match /flashcardSets/{setId} {
      allow read, write: if isOwner(resource.data.userId);
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
    }
    
    // Allow authenticated users to read and write only their own usage data
    match /userUsage/{userId} {
      // Allow read operations only if the ID matches the authenticated user
      allow read: if request.auth != null && request.auth.uid == userId;
      
      // Allow create/update operations only if the ID matches the authenticated user
      allow create, update: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow access to subscription data
    match /subscriptions/{subscriptionId} {
      // Users can read their own subscription data
      allow read: if request.auth != null && 
                    resource.data.userId == request.auth.uid;
      
      // Only admin processes or serverless functions should create/update subscriptions
      // This rule prevents client-side manipulation of subscription data
      allow create, update: if false;
    }
  }
} 