#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to generate missing SEO and PWA assets
 * This creates placeholder assets that can be replaced with proper designs later
 */

const fs = require('fs');
const path = require('path');

// Create a simple SVG icon that can be converted to various formats
const createSVGIcon = () => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="512" height="512" fill="#000000" rx="64"/>
  
  <!-- Flashcard representation -->
  <rect x="64" y="128" width="384" height="256" fill="#ffffff" rx="16" stroke="#e5e5e5" stroke-width="2"/>
  
  <!-- Text lines on flashcard -->
  <rect x="96" y="160" width="320" height="16" fill="#333333" rx="8"/>
  <rect x="96" y="192" width="256" height="12" fill="#666666" rx="6"/>
  <rect x="96" y="216" width="288" height="12" fill="#666666" rx="6"/>
  
  <!-- Divider line -->
  <line x1="96" y1="256" x2="416" y2="256" stroke="#e5e5e5" stroke-width="2"/>
  
  <!-- Answer section -->
  <rect x="96" y="288" width="280" height="12" fill="#666666" rx="6"/>
  <rect x="96" y="312" width="320" height="12" fill="#666666" rx="6"/>
  <rect x="96" y="336" width="240" height="12" fill="#666666" rx="6"/>
  
  <!-- AI indicator (small sparkle) -->
  <circle cx="400" cy="144" r="8" fill="#ffd700"/>
  <path d="M400 136 L404 144 L400 152 L396 144 Z" fill="#ffffff"/>
  <path d="M392 144 L400 148 L408 144 L400 140 Z" fill="#ffffff"/>
</svg>`;
};

// Create a simple OG image SVG
const createOGImageSVG = () => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#000000;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#333333;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="1200" height="630" fill="url(#bg)"/>
  
  <!-- Main title -->
  <text x="600" y="200" font-family="Arial, sans-serif" font-size="72" font-weight="bold" text-anchor="middle" fill="#ffffff">
    Flash Cards AI
  </text>
  
  <!-- Subtitle -->
  <text x="600" y="280" font-family="Arial, sans-serif" font-size="32" text-anchor="middle" fill="#cccccc">
    AI-Powered Flashcard Generator
  </text>
  
  <!-- Features -->
  <text x="600" y="340" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="#aaaaaa">
    📄 Document Upload • 🎤 Text-to-Speech • 📤 Smart Export
  </text>
  
  <!-- Flashcard visual -->
  <rect x="400" y="400" width="400" height="180" fill="#ffffff" rx="12" stroke="#e5e5e5" stroke-width="2"/>
  <rect x="420" y="420" width="360" height="20" fill="#333333" rx="10"/>
  <rect x="420" y="450" width="280" height="16" fill="#666666" rx="8"/>
  <rect x="420" y="475" width="320" height="16" fill="#666666" rx="8"/>
  <line x1="420" y1="510" x2="780" y2="510" stroke="#e5e5e5" stroke-width="2"/>
  <rect x="420" y="530" width="300" height="16" fill="#666666" rx="8"/>
  <rect x="420" y="555" width="240" height="16" fill="#666666" rx="8"/>
  
  <!-- AI sparkle -->
  <circle cx="760" cy="440" r="12" fill="#ffd700"/>
  <path d="M760 428 L766 440 L760 452 L754 440 Z" fill="#ffffff"/>
  <path d="M748 440 L760 446 L772 440 L760 434 Z" fill="#ffffff"/>
</svg>`;
};

// Create apple-touch-icon (simplified version)
const createAppleTouchIconSVG = () => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="180" height="180" viewBox="0 0 180 180" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with rounded corners for iOS -->
  <rect width="180" height="180" fill="#000000" rx="32"/>
  
  <!-- Flashcard -->
  <rect x="20" y="45" width="140" height="90" fill="#ffffff" rx="8" stroke="#e5e5e5" stroke-width="1"/>
  
  <!-- Text lines -->
  <rect x="30" y="55" width="120" height="8" fill="#333333" rx="4"/>
  <rect x="30" y="70" width="90" height="6" fill="#666666" rx="3"/>
  <rect x="30" y="82" width="100" height="6" fill="#666666" rx="3"/>
  
  <!-- Divider -->
  <line x1="30" y1="100" x2="150" y2="100" stroke="#e5e5e5" stroke-width="1"/>
  
  <!-- Answer section -->
  <rect x="30" y="110" width="100" height="6" fill="#666666" rx="3"/>
  <rect x="30" y="122" width="110" height="6" fill="#666666" rx="3"/>
  
  <!-- AI indicator -->
  <circle cx="145" cy="60" r="6" fill="#ffd700"/>
  <path d="M145 54 L148 60 L145 66 L142 60 Z" fill="#ffffff"/>
  <path d="M139 60 L145 63 L151 60 L145 57 Z" fill="#ffffff"/>
</svg>`;
};

function createAssets() {
  console.log('🎨 Generating SEO and PWA assets...\n');

  const publicDir = path.join(__dirname, '..', 'public');
  
  // Ensure public directory exists
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }

  // Create SVG files
  const assets = [
    {
      name: 'flashcard_icon.svg',
      content: createSVGIcon(),
      description: 'Main app icon (SVG)'
    },
    {
      name: 'og-image.svg',
      content: createOGImageSVG(),
      description: 'Open Graph image (SVG)'
    },
    {
      name: 'apple-touch-icon.svg',
      content: createAppleTouchIconSVG(),
      description: 'Apple touch icon (SVG)'
    }
  ];

  assets.forEach(asset => {
    const filePath = path.join(publicDir, asset.name);
    fs.writeFileSync(filePath, asset.content);
    console.log(`✅ Created ${asset.description}: ${asset.name}`);
  });

  // Create a simple favicon.ico placeholder (16x16 black square with white F)
  const faviconSVG = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
  <rect width="16" height="16" fill="#000000"/>
  <text x="8" y="12" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="#ffffff">F</text>
</svg>`;
  
  fs.writeFileSync(path.join(publicDir, 'favicon.svg'), faviconSVG);
  console.log('✅ Created favicon (SVG): favicon.svg');

  // Create robots.txt if it doesn't exist
  const robotsPath = path.join(publicDir, 'robots.txt');
  if (!fs.existsSync(robotsPath)) {
    const robotsContent = `User-agent: *
Allow: /

Sitemap: ${process.env.NEXT_PUBLIC_APP_URL || 'https://flashcards.firebasestudio.io'}/sitemap.xml`;
    
    fs.writeFileSync(robotsPath, robotsContent);
    console.log('✅ Created robots.txt');
  }

  console.log('\n📋 Asset generation completed!');
  console.log('\n⚠️  Note: These are placeholder assets. For production, consider:');
  console.log('  - Converting SVGs to PNG/ICO formats for better browser support');
  console.log('  - Creating high-quality branded icons with proper design');
  console.log('  - Using tools like favicon.io or realfavicongenerator.net');
  console.log('  - Creating multiple sizes for different devices and contexts');
  
  console.log('\n🔧 Next steps:');
  console.log('  1. Replace SVG assets with proper PNG/ICO formats');
  console.log('  2. Test manifest accessibility');
  console.log('  3. Validate SEO metadata');
  console.log('  4. Check PWA installation capabilities');
}

if (require.main === module) {
  createAssets();
}

module.exports = { createAssets };
